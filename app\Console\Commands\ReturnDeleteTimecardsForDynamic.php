<?php

namespace App\Console\Commands;

use App\Models\EntityTag;
use App\Models\EmployeeLeaveRequest;
use App\Models\Timecard;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\DB;

class ReturnDeleteTimecardsForDynamic extends Command
{
    /**
     * The name and signature of the console command.
     * 
     * @var string
     * 
     * --dry-run: Run in dry-run mode without making any changes
     */
    protected $signature = 'app:return-timecards-for-dynamic-users {--dry-run : Run the command in dry-run mode to preview changes without actually applying them}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Command description';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $isDryRun = $this->option('dry-run');
        
        $this->info("this is dry run mode");

        DB::beginTransaction();
        try {
            
            $futureTimecards = Timecard::whereHas('employee.title.workTypePolicy', function ($query) {
                $query->where('work_days_type', '!=', 'fixed_working_hours');
            })
            ->whereDate('from', '>=', '2025-07-23')
            ->whereDate('from', '<', '2025-08-01')
            ->doesntHave('attendance')
            ->whereNull('deleted_at')
            ->get();

            $this->info("Future timecards: " . count($futureTimecards));

            $employeeTimecards = [];
            foreach ($futureTimecards as $timecard) {
                if (!isset($employeeTimecards[$timecard->employee_id])) {
                    $employeeTimecards[$timecard->employee_id] = [];
                }
                $employeeTimecards[$timecard->employee_id][] = date('Y-m-d', strtotime($timecard->from));
            }


            $futureLeaveRequests = DB::table('employee_leave_requests')
            ->join('employees', 'employees.id', '=', 'employee_leave_requests.employee_id')
            ->join('titles', 'titles.id', '=', 'employees.title_id')
            ->join('work_type_policies', 'work_type_policies.id', '=', 'titles.work_type_policy_id')
            ->join('companies', 'companies.id', '=', 'employees.company_id')
            ->where('work_type_policies.work_days_type', '!=', 'fixed_working_hours')
            ->whereColumn('companies.rest_day_leave_id', '!=', 'employee_leave_requests.company_leave_type_id')
            ->whereDate('employee_leave_requests.from', '>=', '2025-07-23')
            ->whereDate('employee_leave_requests.from', '<', '2025-08-01')
            ->whereNull('employee_leave_requests.deleted_at')
            ->get();

            $this->info("Future leave requests: " . count($futureLeaveRequests));

            $employeeLeaveRequests = [];
            foreach ($futureLeaveRequests as $leaveRequest) {
                if (!isset($employeeLeaveRequests[$leaveRequest->employee_id])) {
                    $employeeLeaveRequests[$leaveRequest->employee_id] = [];
                }
                $employeeLeaveRequests[$leaveRequest->employee_id][] = date('Y-m-d', strtotime($leaveRequest->from));
            }

        //////////////////////////////////////////////////////////////////////////////////////////////////////////////////////

            $timecardsToBeReturned = DB::select("
                SELECT t.*, e.company_id
                FROM timecards t
                INNER JOIN employees e ON e.id = t.employee_id
                INNER JOIN titles ti ON ti.id = e.title_id
                INNER JOIN work_type_policies wtp ON wtp.id = ti.work_type_policy_id
                WHERE wtp.work_days_type != 'fixed_working_hours'
                AND t.deleted_at IS NOT NULL
                AND DATE(t.from) >= '2025-07-23'
                AND DATE(t.from) <= '2025-08-01'
                AND t.deleted_at >= '2025-07-21 16:30:00'
                AND t.deleted_at <= '2025-07-21 16:32:00'
            ");

            $this->info("Timecards to be returned: " . count($timecardsToBeReturned));

            $this->info("Returning timecards...");

            $entityTagsToBeCreated = [];

            $timecardsNotReturned = [];
            foreach ($timecardsToBeReturned as $timecard) {
                
                if (isset($employeeTimecards[$timecard->employee_id]) && 
                    in_array(date('Y-m-d', strtotime($timecard->from)), $employeeTimecards[$timecard->employee_id])) {
                    $timecardsNotReturned[] = $timecard->id;
                    continue;
                }

                if (isset($employeeLeaveRequests[$timecard->employee_id]) && 
                    in_array(date('Y-m-d', strtotime($timecard->from)), $employeeLeaveRequests[$timecard->employee_id])) {
                    $timecardsNotReturned[] = $timecard->id;
                    continue;
                }

                $this->info("Returning timecard id: " . $timecard->id);
                DB::table('timecards')
                    ->where('id', $timecard->id)
                    ->update(['deleted_at' => null]);

                $entityTagsToBeCreated[] = [
                    'entity_id' => $timecard->id,
                    'entity_type' => 'time_card',
                    'tag' => 'scheduled',
                    'company' => $timecard->company_id,
                    'created_at' => now(),
                    'updated_at' => now(),
                ];
            }

            EntityTag::insert($entityTagsToBeCreated);

            $this->info("Returning leave requests...");
            $leaveRequestsToBeReturned = DB::select("
                SELECT elr.* 
                FROM employee_leave_requests elr
                INNER JOIN companies as c on c.rest_day_leave_id = elr.company_leave_type_id
                WHERE DATE(elr.from) >= '2025-07-23'
                AND DATE(elr.from) <  '2025-08-01'
                AND elr.deleted_at >= '2025-07-21 16:30:00'
                AND elr.deleted_at <= '2025-07-21 16:32:00'
            ");

            $this->info("Leave requests to be returned: " . count($leaveRequestsToBeReturned));

            $leaveRequestsNotReturned = [];
            foreach ($leaveRequestsToBeReturned as $leaveRequest) {

                $this->info("Employee leave requests not updated because of exists in leaves");

                if (isset($employeeLeaveRequests[$leaveRequest->employee_id]) && 
                    in_array(date('Y-m-d', strtotime($leaveRequest->from)), $employeeLeaveRequests[$leaveRequest->employee_id])) {
                    $leaveRequestsNotReturned[] = $leaveRequest->id;
                    continue;
                }
                $this->info("Employee leave requests not updated because of exists in timecards");

                if (isset($employeeTimecards[$leaveRequest->employee_id]) && 
                    in_array(date('Y-m-d', strtotime($leaveRequest->from)), $employeeTimecards[$leaveRequest->employee_id])) {
                    $leaveRequestsNotReturned[] = $leaveRequest->id;
                    continue;
                }

                $this->info("Returning leave request: " . $leaveRequest->id);
                DB::table('employee_leave_requests')
                    ->where('id', $leaveRequest->id)
                    ->update(['deleted_at' => null]);
            }

            $this->info("Timecards not returned: " . count($timecardsNotReturned));
            $this->info("Leave requests not returned: " . count($leaveRequestsNotReturned));

            $this->info("Timecards not returned: " . json_encode($timecardsNotReturned));
            $this->info("Leave requests not returned: " . json_encode($leaveRequestsNotReturned));

            if ($isDryRun) {
                $this->info("Dry run mode, no changes were made");
                DB::rollBack();
                return;
            }
            DB::commit();

        } catch (\Throwable $th) {
            DB::rollBack();
            throw $th;
        }
    }
}
