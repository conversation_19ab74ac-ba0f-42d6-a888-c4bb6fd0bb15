<?php

namespace App\Services\V1\Attendance\AttendanceActions;

use App\Facades\DistanceFacade;
use App\Jobs\ManualClockOutPushNotificationJob;
use App\Jobs\OvertimeCalculationService;
use App\Models\EarlyClockOutDeductionGroupPolicy;
use App\Repositories\IRepository;
use App\Repositories\Repository;
use App\Repositories\V1\Leaves\EmployeeLeaveRequestRepository;
use App\Services\TimeTracking\BusinessServices\AddRequestsToEmployeeRequest;
use App\Services\TimeTracking\BusinessServices\AssignApprovalRequestToEmployeesService;
use App\Services\TimeTracking\CrudServices\AttendanceCrudService;
use App\Services\TimeTracking\CrudServices\AttendanceOvertimeCrudService;
use App\Services\TimeTracking\CrudServices\AttendanceTagCrudService;
use App\Services\TimeTracking\CrudServices\CicoCrudService;
use App\Services\TimeTracking\CrudServices\EntityTagCrudService;
use App\Traits\CICOHelper;
use App\Traits\DataPreparation;
use App\Traits\WorkflowTrait;
use App\Util\AttendanceUtil;
use Carbon\Carbon;
use Illuminate\Support\Arr;
use stdClass;
use App\Traits\V1\PrepareAssignRequestCycleDataTrait;
use App\Services\V1\AssignApprovalsService;

class VerifiedClockOutAction extends AttendanceAction
{
    use DataPreparation, WorkflowTrait, PrepareAssignRequestCycleDataTrait, CICOHelper;

    private IRepository $attendanceSettingRepository;

    private $policyObj = null;

    private array $tags = [];

    protected $employee = null;

    protected $slot = null;

    protected $clockInDate = null;

    private $clockOut = null;

    private $clockInCico = null;

    protected $clockOutDate = null;

    protected $endShiftDate = null;

    protected $attendance = null;

    protected $timecard = null;

    protected $employeeBasicSalary = 0;

    private $allowedMinutesToClockOutEarly = 0;

    private mixed $endShiftTime;

    private IRepository $overtimeGroupRepository;

    private IRepository $employeeRepository;

    private IRepository $requestCycleRepository;

    private IRepository $newShiftRepository;

    private IRepository $cicoRepository;

    private IRepository $attendanceRepository;

    private IRepository $attendanceOvertime;

    private IRepository $timecardRepository;

    public function __construct(
        private AttendanceCrudService                   $attendanceCrudService,
        private AttendanceTagCrudService                $attendanceTagCrudService,
        private CicoCrudService                         $cicoCrudService,
        private AttendanceOvertimeCrudService           $attendanceOvertimeCrudService,
        private AssignApprovalRequestToEmployeesService $assignApprovalRequestToEmployeesService,
        private AddRequestsToEmployeeRequest            $addRequestsToEmployeeRequest,
        private EntityTagCrudService                    $entityTagCrudService,
        protected EmployeeLeaveRequestRepository        $newEmployeeLeaveRequestRepository,
    )
    {
        $this->attendanceSettingRepository = Repository::getRepository('AttendanceSetting');
        $this->newShiftRepository = Repository::getRepository('NewShift');
        $this->employeeRepository = Repository::getRepository('Employee');
        $this->requestCycleRepository = Repository::getRepository('RequestCycle');
        $this->overtimeGroupRepository = Repository::getRepository('OvertimeGroup');
        $this->cicoRepository = Repository::getRepository('Cico');
        $this->attendanceRepository = Repository::getRepository('Attendance');
        $this->attendanceOvertime = Repository::getRepository('AttendanceOvertime');
        $this->timecardRepository = Repository::getRepository('Timecard');
        $this->allowedMinutesToClockOutEarly = 15;
    }

    public function preformAction(array $request, stdClass &$output): void
    {
        $attendanceData = [];
        $this->employee = $request['employee'];
        $this->employeeBasicSalary = $this->employee->employeeSalary?->basic_salary ?? 0;

        $this->slot = $this->timecardRepository->find($request['slot_id']);
        if (!$this->validateEmployeeSlot($output)) {
            return;
        }

        $this->attendance = $this->slot->attendance;
        if (!$this->validateAttendance($this->attendance, $output)) {
            return;
        }

        $isManualTimeCard = Arr::get($request, 'by_create_manual_card', false); //create manual time card
        $isManualAction = Arr::get($request, 'is_manual_action', false); // make manual clock in or out
        $verifiedAction = isset($request['action']); // approve or edit cico

        if (!$isManualTimeCard && !$verifiedAction) {
            if (!$isManualAction) {
                if (!$this->validateClockOutDeadline($this->slot, $output)) {
                    return;
                } // validate on actual clock in time
            }
            $this->removeAutomatedClockOutIfExists($request);
        }

        $this->clockOutDate = $request['is_manual_action'] == true ? Carbon::parse($request['manual_action_time']) : Carbon::now();
        [$this->endShiftDate, $this->endShiftTime] = $this->getEndShiftTime();

        if (isset($request['is_manual_action']) && $request['is_manual_action']) {
            $this->getClockOutDateFromSlot();
        }

        if (!isset($request['cico_id'])) {
            $this->createCico($request, $output);
            $attendanceData['co_id'] = $output->{'cico'}->id;
        } else {
            $this->updateCico($request);
            $attendanceData['co_id'] = $request['cico_id'];
        }

        $clockOutDifferenceTime = $this->getDifferenceBetweenClockOutAndShiftTimeInMinutes();

        if (!$isManualTimeCard && !$isManualAction && !$verifiedAction) {
            if (!$this->isValidClockOutDifferenceTime($clockOutDifferenceTime, $output)) {
                return;
            }
        }



        //if the employee clock out before the end time of shift
        $earlyClockOutDeductionPolicy = $this->attendanceSettingRepository->getByKey('key', config('globals.ATTENDANCE_SETTINGS.APPLY_EARLY_CLOCK_OUT_DEDUCTION'))->first();
        $earlyClockOutDeductionGroupPolicy = EarlyClockOutDeductionGroupPolicy::where('company_id', config('globals.company')->id)->orderBy('from')->first();
        if($earlyClockOutDeductionPolicy && $earlyClockOutDeductionPolicy->is_used && $earlyClockOutDeductionGroupPolicy){
            $this->allowedMinutesToClockOutEarly = $earlyClockOutDeductionGroupPolicy->from - 1;
        }
        if ($this->endShiftDate > $this->clockOutDate) {
            if ($this->allowedMinutesToClockOutEarly < $clockOutDifferenceTime) {
                $this->tags[] = config(key: 'globals.ATTENDANCE_TAGS.EARLY_CLOCK_OUT');

                $this->addOverTimeWarningMessage($clockOutDifferenceTime, $output);

                // dispatch(new EarlyClockOutPushNotificationJob($this->employee, $output->cico->branch, $earlyClockOutPolicy->value ?? $clockOutDifferenceTime))->afterCommit();
            }
        }
        if($this->isUserCLockedOutEarly() && $earlyClockOutDeductionPolicy && $earlyClockOutDeductionPolicy->is_used){
            $this->createEarlyCLockOutDeductionIfExist($this->employee, $clockOutDifferenceTime, $this->attendance);
        }

        dispatch(new OvertimeCalculationService($this->employee->id, $this->attendance->date))
            ->onConnection(config('globals.OVERTIME_JOB.CONNECTION'))
            ->onQueue(queue: config('globals.OVERTIME_JOB.QUEUE'))->afterCommit();

        if ($request['is_manual_action']) {
            if (isset($request['unverified_out_action'])) {
                if ($request['unverified_out_action'] === 'edited') {
                    $this->tags[] = config('globals.ATTENDANCE_TAGS.MANAGER_CLOCK_OUT');
                } elseif ($request['unverified_out_action'] === 'approved' && !isset($request['unverified_in_action'])) {
                    $this->tags[] = config('globals.ATTENDANCE_TAGS.MANAGER_APPROVED');
                } elseif ($request['unverified_out_action'] === 'approved' && isset($request['unverified_in_action']) && $request['unverified_in_action'] !== 'approved') {
                    $this->tags[] = config('globals.ATTENDANCE_TAGS.MANAGER_APPROVED');
                }
            } else {
                $this->tags[] = config('globals.ATTENDANCE_TAGS.MANAGER_CLOCK_OUT');
            }
        }
        $this->createEntityTags($output);
        if (!empty($output->Error)) {
            return;
        }
        $this->entityTagCrudService->deleteTag($this->attendance->id, 'attendance', config('globals.ATTENDANCE_TAGS.NO_CLOCK_OUT'));
        
        if (isset($request['notes'])) {
            $notes = $this->attendance->notes ?? [];
            $notes[config('globals.ACTIONS_NAMES.ADDED_CLOCK_OUT')] = $request['notes'];
            $request['notes'] = $notes;
        }

        // check if the clock out caused by a create time card request
        $alreadyLogged = isset($request['by_create_manual_card']) ? $request['by_create_manual_card'] : false;

        $attendanceData['id'] = $this->attendance->id;
        $attendanceData['ci_id'] = $this->clockInCico->id;
        if (!$alreadyLogged) {
            unset($request['slot_id']);
            $this->attendanceCrudService->update($attendanceData, $output);
        } else {
            $this->attendanceCrudService->update($attendanceData, $output); // this code won't be logged because it's already logged

        }
        $this->attendance->activities()->first()->update(['properties->attributes->co_id' => $attendanceData['co_id']]);

        if ($request['is_manual_action'] && (!isset($output->actionByManager) || $output->actionByManager === false)) {
            dispatch(new ManualClockOutPushNotificationJob($this->attendance))->afterCommit();
        }

    }

    public function prepareCicoData($request): array
    {
        $data['employee_id'] = $this->employee->id;
        $data['branch_id'] = $request['branch_id'] ?? $this->employee->branch_id;
        $data['date'] = $this->clockOutDate->format('Y-m-d H:i:s');
        $data['cico_by_id'] = $request['is_manual_action'] ? config('globals.user')->employee_id : $this->employee->id;
        $data['lat'] = $request['lat'] ?? null;
        $data['long'] = $request['long'] ?? null;
        $data['in_out'] = AttendanceUtil::CLOCK_OUT;
        $data['source'] = $request['is_manual_action'] ? 'manual' : 'app';
        $data['status'] = isset($request['action']) ? 'approved' : 'verified';
        $data['decider_id'] = isset($request['action']) ? config('globals.user')->employee_id : null;
        $data['by_tablet'] = $request['by_tablet'] ?? false;

        return $data;
    }

    public function createEntityTags(&$output): void
    {
        $data['company'] = config('globals.company')->id;
        foreach ($this->tags as $tag) {
            $data['tag'] = $tag;
            $this->entityTagCrudService->create($data, $output);
            if (!empty($output->Error)) {
                return;
            }
            $this->attendance->entityTags()->save($output->entity_tag);
        }
    }

    public function createCico($request, &$output)
    {
        $this->cicoCrudService->create($this->prepareCicoData($request), $output);
        $this->clockOut = $output->cico;
        if (isset($request['is_manual_action']) && $request['is_manual_action']) {
            $this->clockInCico = $this->cicoRepository->find($this->attendance->ci_id);
        } else {
            $this->clockInCico = $this->cicoRepository->find($this->attendance->ci_id);
        }
        $this->clockInCico->update([
            'paired_clock_id' => $output->cico->id,
        ]);
        $this->clockOut->update([
            'paired_clock_id' => $this->clockInCico->id,
        ]);
    }

    public function updateCico($request)
    {
        $this->clockOut = $this->cicoRepository->find($request['cico_id']);

        $this->clockInCico = $this->cicoRepository->find($this->attendance->ci_id);

        $this->clockInCico->update([
            'paired_clock_id' => $this->clockOut->id,
        ]);
        $this->clockOut->update([
            'paired_clock_id' => $this->clockInCico->id,
        ]);
    }

    public function getDifferenceBetweenClockOutAndShiftTimeInMinutes()
    {
        $actualShiftEndDate = Carbon::parse($this->slot->to);
        if (isset($this->slot->childTimecard)) {
            $actualShiftEndDate = $actualShiftEndDate->min(Carbon::parse($this->slot->childTimecard->from));
        }

        $partialLeave = $this->newEmployeeLeaveRequestRepository->getEmployeeApprovedPartialLeaveAroundDate($this->employee->id, $this->endShiftDate);
        if (isset($partialLeave)) {
            $actualShiftEndDate = $actualShiftEndDate->min(Carbon::parse($partialLeave->from));
        }

        return $this->clockOutDate->diffInMinutes($actualShiftEndDate);
    }

    public function isUserCLockedOutEarly(){
        $actualShiftEndDate = Carbon::parse($this->slot->to);
        if (isset($this->slot->childTimecard)) {
            $actualShiftEndDate = $actualShiftEndDate->min(Carbon::parse($this->slot->childTimecard->from));
        }

        $partialLeave = $this->newEmployeeLeaveRequestRepository->getEmployeeApprovedPartialLeaveAroundDate($this->employee->id, $this->endShiftDate);
        if (isset($partialLeave)) {
            $actualShiftEndDate = $actualShiftEndDate->min(Carbon::parse($partialLeave->from));
        }
        return $this->clockOutDate->lt($actualShiftEndDate);

    }

    public function createAttendanceOvertimeIfExist(&$output, $overtimeMinutes = 0): void // deprecated  #TODO remove it
    {
        if (!is_null($this->policyObj)) {
            $attendanceOvertimeData = $this->prepareAttendanceOvertimeData($overtimeMinutes);
            $this->attendanceOvertimeCrudService->create($attendanceOvertimeData, $output);

            if (!isset($output->overtime)) {
                return;
            }

            $employee = $this->employeeRepository->getById($output->overtime->employee_id, ['branch']);
            if (is_null($employee->branch)) {
                return;
            }

            $systemOvertimeRequestData = $this->prepareEmployeeRequestData(null, $output->overtime, config('globals.EMPLOYEE_REQUEST_NAMES.SYSTEM_OVERTIME'));
            $this->addRequestsToEmployeeRequest->perform($systemOvertimeRequestData, $output);

            // activity()
            // ->withoutLogs(
            //     function () use ($systemOvertimeRequestData, $output) {
            // });

            // $cycle = $this->requestCycleRepository->getRelatedCycle(
            //     'attendance_overtime'
            // );

            // //these titles already have a permission to do an action on overtime request
            // $titlesIds = array_column($cycle->toArray(), 'title_id');

            // $approvingEmployees = $this->employeeRepository->getEmployeesWithCommonBranchAndTitles($employee->branch->id, $titlesIds);

            // if (count($approvingEmployees) > 0) {
            //     $overtimeRequest = $this->prepareApprovalCycleRequests($cycle, $approvingEmployees, $output->overtime, $output->employee_request->id);
            // $this->assignApprovalRequestToEmployeesService->perform($overtimeRequest, $output);
            // }

            $assignApprovalsService = app(AssignApprovalsService::class);
            $assignApprovalsService->assignApprovals($this->prepareAssignRequestCycleData('attendance_overtime', $output->overtime, [$employee->title->role_id]));

            unset($output->overtime);
            unset($output->employee_request);

            // dispatch(new OverTimeRequestPushNotificationJob($approvingEmployees, $this->employee))->afterCommit(); // done
        }
    }

    public function setOverTimePolicy($overTimeValue): void
    {
        $titleId = $this->employee->title->id;

        //if clock out difference time between overtime polices range
        $overtimeGroup = $this->overtimeGroupRepository->getOvertimeGroupRelatedToTitleWithOvertimePolicy($titleId, $overTimeValue);
        if (is_null($overtimeGroup)) {
            //if the worker work more than all overtime ranges, so get max policy
            $overtimeGroup = $this->overtimeGroupRepository->getOvertimeGroupRelatedToTitleWithMaxOvertimePolicy($titleId);
        }
        if (!is_null($overtimeGroup) and count($overtimeGroup->dailyOvertimePolicies) > 0) {
            $this->policyObj = $overtimeGroup->dailyOvertimePolicies[0];
        }
    }

    public function isValidClockOutDifferenceTime(int $numberOfMins, stdClass &$output): bool
    {
        if ($numberOfMins > 24 * 60) {
            $output->Error = ['You can not clock out after 24 hours from your slot time',
                'لا يمكنك تسجيل الخروج بعد 24 ساعة من معاد الوردية الخاص بك'];

            return false;
        }

        return true;
    }

    public function removeAutomatedClockOutIfExists($request): void
    {
        $cico = $this->cicoRepository->getLastCico($request['employee']->id);
        if ($cico->in_out === AttendanceUtil::CLOCK_OUT && $cico->source === AttendanceUtil::CICO_AUTOMATED_STATUS) {
            // Find and update any attendance records that reference this cico
            $attendance = $this->attendanceRepository->getByKey('co_id', $cico->id)->first();
            if ($attendance) {
                $attendance->update(['co_id' => null]);
            }
            $cico->delete();
        }
    }

    public function validateAttendance($attendance, &$output): bool
    {
        if (!isset($attendance)) {
            $output->Error = ['This employee does not have a verified clock in on this slot',
                'هذا العامل ليس لديه تسجيل حضور على هذا الورديه'];

            return false;
        }

        if (!$this->validateClockOut($attendance)) {
            $output->Error = ['You can not edit the clock out time set by another user',
                'لا يمكن التعديل على بطاقة الخروج التي تم تعيينها من قبل موظف آخر'];

            return false;
        }

        return true;
    }

    public function validateClockOut($attendance): bool
    {
        if (is_null($attendance->co_id)) {
            return true;
        }
        $clockOut = $this->cicoRepository->getById($attendance->co_id);
        if ($clockOut->source == AttendanceUtil::CICO_AUTOMATED_STATUS ||
        ($clockOut->source = 'manual' && $clockOut->cico_by_id == config('globals.user')->employee_id)) {
            return true;
        }
        return false;
    }

    public function validateClockOutDeadline($slot, &$output): bool
    {
        $clockOutDeadline = $this->attendanceSettingRepository->findAttendanceSetting(AttendanceUtil::CLOCK_OUT_DEADLINE);

        if (Carbon::now()->greaterThan(Carbon::parse($slot->to)->addMinutes($clockOutDeadline->value ?? 0))) {
            $output->Error = ['This employee has passed the clock out deadline',
                'هذا العامل قد تجاوز وقت تسجيل الخروج'];

            return false;
        }

        return true;
    }

    public function validateVerifiedLocation(array $request, stdClass &$output): bool
    {
        $role = auth()->user()->roles->first();
        if ($role && !$request['is_manual_action'] && ($role->hasPermissionTo('any_location', 'user-api')
                || $role->hasPermissionTo('any_branch', 'user-api'))) {
            return true;
        }
        [$latitude, $longitude, $radius] = $this->userAttendance->getClockOutLocation($this->slot, $this->employee);
        $distance = DistanceFacade::getDistance($latitude, $longitude, $request['lat'], $request['long']);
        if ($distance > $radius) {
            $output->Error = ['You are not in the range of your branch', 'انت لست في نطاق فرعك'];

            return false;
        }

        return true;
    }

    public function addOverTimeWarningMessage($clockOutDifferenceTime, stdClass $output): void
    {
        $clockOutDifferenceTimeInHours = $clockOutDifferenceTime / 60;
        $clockOutDifferenceTimeInHoursEn = '';
        $clockOutDifferenceTimeInHoursAr = '';
        if (is_float($clockOutDifferenceTimeInHours)) {
            $clockOutDifferenceTimeInHours = floor($clockOutDifferenceTimeInHours);
            $clockOutDifferenceTimeInHoursEn = 'More Than ';
            $clockOutDifferenceTimeInHoursAr = 'اكثر من ';
        }
        $output->Warning[] = [
            'You still have ' . $clockOutDifferenceTimeInHoursEn . $clockOutDifferenceTimeInHours . ' hours left in your shift, are you sure you want to clock out early',
            ' متبقى لديك ' . $clockOutDifferenceTimeInHoursAr . $clockOutDifferenceTimeInHours . ' ساعات فى الشيفت الحالى, هل تريد تسجيل الخروج مبكرا ',
        ];
    }

    private function getWorkerWorkDurationPerDay($todayAttendances)
    {
        $workedDurationInMinutes = 0;
        foreach ($todayAttendances as $attendance) {
            $ci_id = $attendance->ci_id;
            $clockIn = $this->cicoRepository->find($ci_id);

            $this->clockOut = $this->cicoRepository->find($clockIn->paired_clock_id);

            $workedDurationInMinutes += Carbon::parse($clockIn->date)->diffInMinutes(Carbon::parse($this->clockOut->date));
        }

        return $workedDurationInMinutes;
    }

    public function getOverTimeValue($todayAttendances, $pastWorkedDuration, $titleWorkingInMinutes, $policyValue): int
    {
        $workerDuration = $this->getWorkerWorkDurationPerDay($todayAttendances);
        $pastOverTime = (max($workerDuration - $titleWorkingInMinutes, 0));

        return $pastOverTime > $policyValue ? ($pastWorkedDuration - $titleWorkingInMinutes - (max($workerDuration - $titleWorkingInMinutes, 0))) : $pastWorkedDuration - $titleWorkingInMinutes;
    }

    public function getClockOutDateFromSlot()
    {
        $clockOutTime = $this->clockOutDate->format('H:i:s');
        $endShiftDate = $this->endShiftDate->format('Y-m-d');

        $clockIn = $this->cicoRepository->find($this->attendance->ci_id);
        $clockInTime = Carbon::parse($clockIn->date)->format('H:i:s');

        $startShiftDate = Carbon::parse($this->slot->from)->format('Y-m-d');

        if ($clockOutTime < $clockInTime && $startShiftDate === $endShiftDate) {
            $endShiftDate = $this->endShiftDate->addDay()->format('Y-m-d');
        }
        else if($clockOutTime > $clockInTime && Carbon::parse($this->clockOutDate)->toDateString() <  $endShiftDate){
            $endShiftDate = Carbon::parse($this->clockOutDate)->toDateString();
        }

        $this->clockOutDate = Carbon::parse($endShiftDate . ' ' . $clockOutTime);
    }

    public function getTitleWorkingDurationInMinutes($title, $clockOutDate)
    {
        $event = $title->events->where('start_date', '<=', $clockOutDate)->where('end_date', '>=', $clockOutDate)->first();
        $isThereEventRunning = isset($event);
        if ($isThereEventRunning) {
            return $event->working_minutes;
        }

        return optional($title)->working_hours * 60;
    }

    public function validateEmployeeSlot(stdClass &$output): bool
    {
        if (is_null($this->slot)) {
            $output->Error = ['This employee not assigned to this slot',
                'هذا العامل لم يتم تعينه على هذا الوردية من قبل'];

            return false;
        }

        return true;
    }

    public function getEndShiftTime(): array
    {
        $this->endShiftDate = Carbon::parse($this->slot->to);
        $this->endShiftTime = Carbon::parse($this->slot->to)->format('H:i:s');

        return [$this->endShiftDate, $this->endShiftTime];
    }
}
