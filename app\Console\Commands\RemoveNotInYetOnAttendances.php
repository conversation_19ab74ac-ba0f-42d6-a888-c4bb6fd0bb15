<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use App\Models\Timecard;

class RemoveNotInYetOnAttendances extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'app:remove-not-in-yet-on-attendances';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Command description';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $this->info('Starting to process timecards with not_in_yet tag and on_time/late attendance tags...');

        $timecards = Timecard::with(['entityTags', 'attendance.entityTags'])
            ->whereHas('entityTags', function ($query) {
                $query->where('tag', 'not_in_yet');
            })
            ->whereHas('attendance')
            // ->where(function ($query) {
            //     $query->whereHas('attendance.entityTags', function ($q) {
            //         $q->whereIn('tag', ['on_time', 'late']);
            //     });
            // })
            ->whereDate('from', '<=', date('Y-m-d'))
            ->get();

        $this->info("Found {$timecards->count()} timecards to process.");

        $processedCount = 0;
        foreach ($timecards as $timecard) {
            $this->line("Processing timecard ID: {$timecard->id}");
            
            // Get the attendance tags for logging
            $attendanceTags = $timecard->attendance->entityTags->pluck('tag')->join(', ');
            $this->line("Timecard has attendance tags: {$attendanceTags}");

            // Remove the not_in_yet tag
            $deleted = $timecard->entityTags()
                ->where('tag', 'not_in_yet')
                ->delete();

            if ($deleted) {
                $processedCount++;
                $this->line("✓ Successfully removed not_in_yet tag from timecard ID: {$timecard->id}");
            } else {
                $this->warn("! Failed to remove not_in_yet tag from timecard ID: {$timecard->id}");
            }
        }

        $this->info("Command completed. Successfully processed {$processedCount} out of {$timecards->count()} timecards.");
    }
}
