<?php

namespace App\Services\V1\EmployeeProfile;

use App\Enums\EmployeeInfo\ContractDurationEnum;
use App\Enums\EmployeeInfo\MaritalStatusEnum;
use App\Enums\EmployeeInfo\Nationalities;
use App\Enums\EmployeeInfo\ReligionEnum;
use App\Exceptions\UnprocessableException;
use App\FeatureToggles\Unleash;
use App\Http\Resources\V1\EmployeeProfile\EmployeeContractResource;
use App\Http\Resources\V1\EmployeeProfile\EmployeeEducationResource;
use App\Http\Resources\V1\EmployeeProfile\EmployeeEmergencyContactResource;
use App\Http\Resources\V1\LoansAndSalaryAdvances\ScopeEmployeeForLoansResource;
use App\Jobs\V1\FillEmployeeBaseBalancesJob;
use App\Models\DeviceIdResetHistory;
use App\Models\EmployeeFaceIdImage;
use App\Models\EmployeeInfo;
use App\Repositories\BaseRepository;
use App\Repositories\NewDepartmentRepository;
use App\Repositories\NewEmployeeInfoRepository;
use App\Repositories\NewEmployeeRepository;
use App\Repositories\NewRoleRepository;
use App\Repositories\NewTitleRepository;
use App\Repositories\PayrollRepositories\EmployeePayrollSummaryRepository;
use App\Repositories\PayrollRepositories\EmployeeSalaryRepository;
use App\Repositories\PayrollRepositories\PayrollsRepository;
use App\Repositories\PayrollRepositories\SalaryComponentsCategoryRepository;
use App\Repositories\TerminationRequestRepository;
use App\Repositories\V1\BranchRepository;
use App\Repositories\V1\Employee\ChangeTitleRepository;
use App\Repositories\V1\EmployeeRepository;
use App\Repositories\V1\EmployeeRequestRepository;
use App\Repositories\V1\Files\AttachmentRepository;
use App\Repositories\V1\KPIs\IncentiveParametersRepository;
use App\Repositories\V1\KPIs\IncentivePerEmployeesRepository;
use App\Repositories\V1\WorkFlow\WorkflowApprovalCycleRepository;
use App\Services\BaseService;
use App\Services\CompanySetup\CrudServices\RegisterationValidationCrudService;
use App\Services\CompanySetup\CrudServices\UserCrudService;
use App\Services\CompanySetup\EmployeesService;
use App\Services\LeaveManagement\CrudServices\EmployeeLeaveRequestCrudService;
use App\Services\TerminationService;
use App\Services\V1\Employee\EmployeeContractService;
use App\Services\V1\Employee\EmployeeEducationService;
use App\Services\V1\Employee\EmployeeEmergencyContactService;
use App\Services\V1\KPIs\IncentiveService;
use App\Traits\QueriesHelper;
use App\Traits\UploadFile\UploadFile;
use App\Traits\V1\LogTrait;
use App\Traits\V1\Payroll\PayrollHelper;
use App\Traits\V1\PhoneHelper;
use App\Util\PayrollUtil;
use App\Util\ScopeUtil;
use Carbon\Carbon;
use Illuminate\Support\Facades\DB;
use Ramsey\Uuid\Uuid;

class EmployeeProfileService extends BaseService
{
    use LogTrait, PayrollHelper, PhoneHelper, QueriesHelper, UploadFile;

    private BaseRepository $departmentRepository;

    private BaseRepository $titlesRepository;

    private BaseRepository $branchRepository;

    private BaseRepository $salaryComponentCategoryRepository;

    private NewRoleRepository $roleRepository;

    private PayrollsRepository $payrollRepository;

    private EmployeePayrollSummaryRepository $employeePayrollSummaryRepository;

    private PayrollsRepository $payrollsRepository;

    private TerminationRequestRepository $terminationRequestRepository;

    private WorkflowApprovalCycleRepository $workflowApprovalCycleRepository;

    private EmployeeRequestRepository $employeeRequestRepository;

    private ChangeTitleRepository $changeTitleRepository;

    private $newEmployeeRepository;

    private $incentiveParameterRepository;

    private $incentivePerEmployeeRepository;

    public function __construct(EmployeeRepository $repository,
        private AttachmentRepository $attachmentRepository,
        private EmployeeEmergencyContactService $emergencyContactService,
        private EmployeeEducationService $educationService,
        private EmployeeContractService $contractService,
        private UserCrudService $userCrudService,
        private NewEmployeeInfoRepository $employeeInfoRepository,
        private EmployeesService $employeesService,
        private RegisterationValidationCrudService $registerationValidationCrudService,
        private EmployeeSalaryRepository $employeeSalaryRepository,
        private EmployeeLeaveRequestCrudService $employeeLeaveRequestCrudService,
        private IncentiveService $incentiveService,
        private TerminationService $terminationService,
    ) {
        $this->repository = $repository;
        $this->titlesRepository = new NewTitleRepository;
        $this->departmentRepository = new NewDepartmentRepository;
        $this->branchRepository = new BranchRepository;
        $this->salaryComponentCategoryRepository = new SalaryComponentsCategoryRepository;
        $this->roleRepository = new NewRoleRepository;
        $this->payrollRepository = new PayrollsRepository;
        $this->employeePayrollSummaryRepository = new EmployeePayrollSummaryRepository;
        $this->payrollsRepository = new PayrollsRepository;
        $this->terminationRequestRepository = new TerminationRequestRepository;
        $this->workflowApprovalCycleRepository = new WorkflowApprovalCycleRepository;
        $this->employeeRequestRepository = new EmployeeRequestRepository;
        $this->newEmployeeRepository = new NewEmployeeRepository;
        $this->changeTitleRepository = new ChangeTitleRepository;
        $this->incentiveParameterRepository = new IncentiveParametersRepository;
        $this->incentivePerEmployeeRepository = new IncentivePerEmployeesRepository;
    }

    public function list($data)
    {
        $userBranches = auth()->user()->employee->branches->pluck('id')->toArray();
        $userCanManageCompany = config('globals.user')->canManageCompany();
        if (! $userCanManageCompany) {
            if (isset($data['branch_ids'])) {
                $data['branch_ids'] = array_intersect($data['branch_ids'], $userBranches);
            } else {
                $data['branch_ids'] = $userBranches;
            }
        }

        $employees = $this->repository->list($data);

        foreach ($employees as $employee) {
            // Retrieve user roles and check permissions
            $this->addActionButtons($employee);
            //            if($employee->employeeInfo->termination_date != null && $employee->status == 'active'){
            //                $employee->status = 'termination_pending';
            //            }
        }

        return $employees;
    }

    public function listForBulkEdit($data)
    {
        $userBranches = auth()->user()->employee->branches->pluck('id')->toArray();
        $userCanManageCompany = config('globals.user')->canManageCompany();
        if (! $userCanManageCompany) {
            if (isset($data['branch_ids'])) {
                $data['branch_ids'] = array_intersect($data['branch_ids'], $userBranches);
            } else {
                $data['branch_ids'] = $userBranches;
            }
        }

        return $this->repository->listForBulkEdit($data);
    }

    public function getEmployeeNames($data)
    {
        $importantData = $this->repository->getEmployeeNames($data);

        return $importantData;
    }

    public function getEmployeeInfo($id)
    {
        $employee = $this->repository->getEmployeeInfo($id);
        if (is_null($employee)) {
            throw new UnprocessableException('Employee not found');
        }
        $this->addActionButtons($employee);
        $employee->scope = $this->getUserHighestScopeKey($employee->user);
        $unleash = app(Unleash::class);
        if ($unleash->isEnhancedEmployeeProfileEnabled()) {
            $employee->managedEmployees = $employee->scope == ScopeUtil::COMPANY_SCOPE ? collect([]) : $this->newEmployeeRepository->getEmployeeUnderScope($employee, $employee->scope, null, false, false);
            $userRoles = config('globals.user')->roles;
            $hasPermissionToResetDevice = $this->hasPermission($userRoles, 'reset_device_id');
            $employee->can_reset_device = $this->canResetDeviceId($employee->id) && $hasPermissionToResetDevice;
        }

        return $employee;
    }

    public function getEmployeeSalaryInfo($id)
    {
        $employee = $this->repository->find($id);
        if (is_null($employee)) {
            throw new UnprocessableException('Employee not found');
        }
        $employeeSalaryInfo = $this->repository->getEmployeeSalaryInfo($id);
        $allowances = $this->salaryComponentCategoryRepository->getEmployeeAllowances($employee->branch_id, $employee->title_id, $employee->id);

        return [$employeeSalaryInfo, $allowances];
    }

    public function getEmployeeRoles($id)
    {
        $employee = $this->repository->find($id);
        if (is_null($employee)) {
            throw new UnprocessableException('Employee not found');
        }

        return $this->roleRepository->getEmployeeRolesWithPermissions($id);
    }

    public function assignRoleToEmployee($employee_id, $role_id)
    {
        $employee = $this->repository->find($employee_id);
        if (is_null($employee)) {
            throw new UnprocessableException('Employee not found');
        }
        $role = $this->roleRepository->find($role_id);
        if (is_null($role)) {
            throw new UnprocessableException('Role not found');
        }

        $user = $employee->user;
        if ($user->hasRole($role)) {
            throw new UnprocessableException('User has already this role');
        }
        $oldUserRoles = $user->roles->pluck('id')->toArray();
        $this->assignRole($user, $role);

        $highestScopeKey = $this->getUserHighestScopeKey($user);
        if ($highestScopeKey == ScopeUtil::COMPANY_SCOPE) {
            $branches = $this->branchRepository->all()->pluck('id')->toArray();
            $employee->branches()->sync($branches);
        }

        activity('assign_role')
            ->on($employee)
            ->causedBy(auth()->user()->employee)
            ->withProperties(['data' => ['employee_id' => $employee_id, 'old_role_ids' => $oldUserRoles, 'new_role_id' => $role_id]])
            ->event('assign_role')
            ->log('employee id '.auth()->user()->employee_id.' is trying to do '.'assign_role'.' action');
    }

    public function assignRole($user, $role)
    {
        $user->syncRoles($role);
    }

    public function getEmployeePayslip($id, $data)
    {
        $employee = $this->repository->find($id);

        if (is_null($employee)) {
            throw new UnprocessableException('Employee not found');
        }

        // $isThatYourPayslip = $employee->id == auth()->user()->employee_id;
        // if(!request()->bearerToken() && JWTAuth::parseToken()->getPayload()->get('admin_id')){
        //     if(!$isThatYourPayslip){
        //         throw new UnprocessableException('You are not allowed to view this payslip');
        //     }
        // }

        $payroll = $this->payrollRepository->getPayrollByMonthAndYear($data['month'], $data['year']);
        if (is_null($payroll) || $payroll->status != 'finalized' || ! $payroll->is_payslips_published) {
            return null;
        }

        $unleash = app(Unleash::class);

        $sickLeaveType = $unleash->getSickLeaveComponentEditsFeatureFlag() ? config('globals.company')?->sickLeaveType : null;

        $employeeSummary = $this->employeePayrollSummaryRepository->getEmployeeSummary($payroll->id, $id,
            $payroll->start, $payroll->end, $sickLeaveType?->id ?? null);

        $hoolidayCompensatedDays = $this->getHolidayPayoutCompensations($employeeSummary);
        $totalAdditions = 0;
        $additionCategories = $deductionCategories = [];
        if (! is_null($employeeSummary)) {
            $employeeSummary->net_salary = $employeeSummary->employee?->employeeSalary?->net_salary ?? 0;
            foreach ($employeeSummary->payroll->employeeSalaryComponentMonth as $employeeSalaryComponent) {
                if (! isset($employeeSalaryComponent->salaryComponent) || ! isset($employeeSalaryComponent->salaryComponent->salaryComponentsCategory)) {
                    continue;
                }
                $categoryName = $employeeSalaryComponent->salaryComponent->salaryComponentsCategory->name;
                $componentName = $employeeSalaryComponent->salaryComponent->name;

                if ($employeeSalaryComponent->salaryComponent->salaryComponentsCategory->is_addition) {
                    $totalAdditions += $employeeSalaryComponent->amount;
                    $salaryComponents = array_key_exists($categoryName, $additionCategories) ? $additionCategories[$categoryName]['salary_components'] : [];
                    if (! isset($additionCategories[$categoryName])) {
                        $additionCategories[$categoryName] = [
                            'id' => $employeeSalaryComponent->salaryComponent->salaryComponentsCategory->id,
                            'name' => $categoryName,
                            'total' => 0,
                        ];
                    }
                    $additionCategories[$categoryName]['salary_components'][] = [
                        'id' => $employeeSalaryComponent->salaryComponent->id,
                        'name' => $componentName,
                        'amount' => $employeeSalaryComponent->amount,
                        'details' => [],
                    ];
                    $additionCategories[$categoryName]['total'] += $employeeSalaryComponent->amount;

                } else {

                    $salaryComponents = array_key_exists($categoryName, $deductionCategories) ? $deductionCategories[$categoryName]['salary_components'] : [];

                    if (! isset($deductionCategories[$categoryName])) {
                        $deductionCategories[$categoryName] = [
                            'id' => $employeeSalaryComponent->salaryComponent->salaryComponentsCategory->id,
                            'name' => $categoryName,
                            'total' => 0,
                        ];
                    }
                    $deductionCategories[$categoryName]['salary_components'][] = [
                        'id' => $employeeSalaryComponent->salaryComponent->id,
                        'name' => $componentName,
                        'amount' => $employeeSalaryComponent->amount,
                        'details' => [],
                    ];
                    $deductionCategories[$categoryName]['total'] += $employeeSalaryComponent->amount;

                }

                if ($componentName == 'overtime') {

                    $overtimeIndex = $this->getSalaryComponentIndex($additionCategories[$categoryName]['salary_components'], $componentName);
                    if ($overtimeIndex >= 0) {
                        $overtimes = $employeeSummary->employee->attendanceOvertimes->where('date', '>=', $payroll->start)->where('date', '<=', $payroll->end);
                        foreach ($overtimes as $overtime) {
                            $titleWorkingHours = $employeeSummary?->employee?->title?->working_hours ?? PayrollUtil::DEFAULT_HOURS_PER_DAY;
                            $amount = ($employeeSummary->gross_salary / PayrollUtil::PAYROLL_MONTH_DAYS / $titleWorkingHours) *
                                (($overtime->overtime_minutes / 60) * $overtime->overtime_value);
                            $additionCategories[$categoryName]['salary_components'][$overtimeIndex]['details'][] = [
                                'date' => $overtime->date,
                                'amount' => $amount,
                            ];

                        }
                    }

                } elseif ($componentName == 'late_deduction') {
                    $lateDeductionIndex = $this->getSalaryComponentIndex($deductionCategories[$categoryName]['salary_components'], $componentName);
                    if ($lateDeductionIndex >= 0) {
                        $deductions = $employeeSummary->employee->attendanceDeductions->where('date', '>=', $payroll->start)->where('date', '<=', $payroll->end);
                        foreach ($deductions as $deduction) {

                            $amount = round($deduction->deduction_value * ($employeeSummary->gross_salary / PayrollUtil::PAYROLL_MONTH_DAYS), 2);
                            $deductionCategories[$categoryName]['salary_components'][$lateDeductionIndex]['details'][] = [
                                'date' => $deduction->date,
                                'amount' => $amount,
                                'reason' => 'late',
                            ];

                        }
                    }

                } elseif ($componentName == 'early_clockout_deduction') {
                    $isEarlyClockOutFlagEnabled = $unleash->isEarlyClockoutDeductionEnabled();
                    $earlyClockOutDeductionIndex = $this->getSalaryComponentIndex($deductionCategories[$categoryName]['salary_components'], $componentName);
                    if ($earlyClockOutDeductionIndex >= 0 && $isEarlyClockOutFlagEnabled) {
                        $deductions = $employeeSummary->employee->attendanceEarlyClockOutDeductions->where('date', '>=', $payroll->start)->where('date', '<=', $payroll->end);
                        foreach ($deductions as $deduction) {
                            $amount = round($deduction->deduction_value * ($employeeSummary->gross_salary / PayrollUtil::PAYROLL_MONTH_DAYS), 2);
                            $deductionCategories[$categoryName]['salary_components'][$earlyClockOutDeductionIndex]['details'][] = [
                                'date' => $deduction->date,
                                'amount' => $amount,
                                'reason' => 'early_clock_out',
                            ];
                        }
                    }
                } elseif ($componentName == 'absence_deduction') {
                    $absenceDeductionIndex = $this->getSalaryComponentIndex($deductionCategories[$categoryName]['salary_components'], $componentName);
                    if ($absenceDeductionIndex >= 0) {
                        $timecards = $employeeSummary->employee->timecards->where('from', '>=', $payroll->start)->where('from', '<=', $payroll->end);
                        foreach ($timecards as $timecard) {
                            $tag = $this->getTag($timecard);
                            $amount = $this->getDeductionAmount($tag, $employeeSummary->gross_salary);
                            $deductionCategories[$categoryName]['salary_components'][$absenceDeductionIndex]['details'][] = [
                                'date' => Carbon::parse($timecard->from)->format('Y-m-d'),
                                'amount' => round($amount, 2),
                                'reason' => $tag,
                            ];

                        }

                    }
                } elseif ($unleash->getSickLeaveComponentEditsFeatureFlag() && $componentName == PayrollUtil::SICK_LEAVE_DEDUCTION) {
                    $sickLeaveDeductionIndex = $this->getSalaryComponentIndex($deductionCategories[$categoryName]['salary_components'], $componentName);
                    if ($sickLeaveDeductionIndex >= 0) {
                        foreach ($employeeSummary->employee->employeeLeaveRequests as $leaveRequest) {
                            $leaveDays = $this->getLeaveIntersectingDaysWitPayroll($payroll, $leaveRequest);
                            $sickLeaveDeductionPercentage = $sickLeaveType?->leave_deduction_percentage ?? 0;
                            $amount = ceil(($employeeSummary->gross_salary / PayrollUtil::PAYROLL_MONTH_DAYS) * $leaveDays * $sickLeaveDeductionPercentage);
                            $deductionCategories[$categoryName]['salary_components'][$sickLeaveDeductionIndex]['details'][] = [
                                'from' => $leaveRequest->from,
                                'to' => $leaveRequest->to,
                                'amount' => $amount,
                                'reason' => 'Sick leave',
                            ];
                        }

                    }

                }
            }

            // catculat category amount total
            foreach ($additionCategories as &$category) {
                foreach ($category['salary_components'] as &$component) {
                    if (count($component['details']) > 0) {
                        $component['amount'] = array_reduce($component['details'], function ($carry, $item) {
                            return $carry + $item['amount'];
                        }, 0);
                    }
                }

                $category['total'] = array_reduce($category['salary_components'], function ($carry, $component) {
                    return $carry + $component['amount'];
                }, 0);

                round($category['total'], 2);
            }

            // calculate deduction category amount total
            foreach ($deductionCategories as &$category) {
                foreach ($category['salary_components'] as &$component) {
                    if (count($component['details']) > 0) {
                        $component['amount'] = array_reduce($component['details'], function ($carry, $item) {
                            return $carry + $item['amount'];
                        }, 0);
                    }
                }

                $category['total'] = array_reduce($category['salary_components'], function ($carry, $component) {
                    return $carry + $component['amount'];
                }, 0);

                round($category['total'], 2);
            }

            // calculate total additions
            $totalAdditions = $this->calculateTotalAdditions(array_values($additionCategories));

            // calculate total deductions from category totals
            $totalDeductionsFromCategories = 0;
            foreach ($deductionCategories as $category) {
                $totalDeductionsFromCategories += $category['total'];
            }

            return [
                'net_salary' => $employeeSummary?->net_salary ?? 0,
                'final_net_salary' => $employeeSummary?->final_net_salary ?? 0,
                'gross_salary' => $employeeSummary?->gross_salary ?? 0,
                'tax_amount' => $employeeSummary?->tax_amount ?? 0,
                'insurance_amount' => $employeeSummary?->insurance_amount ?? 0,
                'holidays_compensated_days' => $hoolidayCompensatedDays,
                'total' => ! is_null($employeeSummary) ? $employeeSummary->tax_amount + $employeeSummary->insurance_amount : 0,
                'additions' => [
                    'total' => round($totalAdditions, 2),
                    'categories' => array_values($additionCategories),
                ],
                'deductions' => [
                    'total' => round($totalDeductionsFromCategories, 2),
                    'categories' => array_values($deductionCategories),
                ],
            ];

        }
    }

    public function calculateTotalAdditions(array $additionCategories)
    {
        $totalAdditions = 0;
        foreach ($additionCategories as $category) {
            foreach ($category['salary_components'] as $salaryComponent) {
                $totalAdditions += $salaryComponent['amount'];
            }
        }

        return $totalAdditions;
    }

    public function getSalaryComponentIndex($salaryComponents, $componentName)
    {
        foreach ($salaryComponents as $index => $salaryComponent) {
            if ($salaryComponent['name'] == $componentName) {
                return $index;
            }
        }

        return -1;
    }

    public function getTag($timecard)
    {
        if (! is_null($timecard->attendance)) {
            return PayrollUtil::ABSENCE_TYPES['CONSIDERED_ABSENT'];
        }

        return $timecard->entityTags->first()->tag;
    }

    public function getDeductionAmount($tag, $netSalary)
    {
        if ($tag == PayrollUtil::ABSENCE_TYPES['ABSENT'] || $tag == PayrollUtil::ABSENCE_TYPES['CONSIDERED_ABSENT']) {
            return round(($netSalary / PayrollUtil::PAYROLL_MONTH_DAYS) * PayrollUtil::ABSENCE_WITH_PERMISSION_DEDUCTION_VALUE, 2);
        } elseif ($tag == PayrollUtil::ABSENCE_TYPES['ABSENT_WITHOUT_PERMISSION']) {
            return round(($netSalary / PayrollUtil::PAYROLL_MONTH_DAYS) * PayrollUtil::ABSENCE_WITHOUT_PERMISSION_DEDUCTION_VALUE, 2);
        }

    }

    public function getEmployeeProfilePicture(int $id)
    {
        return $this->attachmentRepository->getEmployeeProfilePicture($id);
    }

    public function uploadProfilePicture(array $data)
    {
        $employee = $this->repository->findOrFail($data['employee_id']);
        $companyUuid = $employee->company->uuid;
        $employeeUuid = $employee->uuid;
        $filePath = $this->uploadFile($data['profile_picture'], 's3', "{$companyUuid}/employee-files/{$employeeUuid}/profile-picture");

        return $this->saveProfilePicture(
            $filePath,
            $data['employee_id']
        );
    }

    public function saveProfilePicture($filePath, $employeeId)
    {
        $fileUrl = $this->getFileUrl($filePath);
        $attachmentData = $this->prepareAttachment($filePath, $employeeId);

        return $this->attachmentRepository->upsert(
            $attachmentData,
            ['attachable_id' => $employeeId,
                'attachable_type' => 'employee'],
            ['path' => $filePath,
                'attachment_url' => $fileUrl]
        )->get();
    }

    public function prepareAttachment($filePath, $employeeId)
    {
        $fileUrl = $this->getFileUrl($filePath);

        return $attachmentData = [
            'attachable_type' => 'employee',
            'attachable_id' => $employeeId,
            'path' => $filePath,
            'attachment_url' => $fileUrl,
            'created_at' => date('Y-m-d H:i:s'),
            'updated_at' => date('Y-m-d H:i:s'),
        ];
    }

    public function addActionButtons(mixed $employee): void
    {
        $user = auth()->user();
        $userRoles = $user->roles;

        // Encapsulated permission checks
        $hasPermissionToLiteTerminate = $this->hasPermission($userRoles, 'lite_terminate');
        $hasPermissionToTerminate = $this->hasPermission($userRoles, 'terminate_employee');
        $hasPermissionToRehire = $this->hasPermission($userRoles, 'add_employee');
        $hasPermissionToCancel = $this->hasPermission($userRoles, 'terminate_employee');
        $hasPermissionToResetDevice = $this->hasPermission($userRoles, 'reset_device_id');

        // Check employee status and payroll conditions
        $isNotTerminated = isset($employee->status) && $employee->status != 'terminated';
        $currentPayroll = $this->payrollsRepository->getLastDraftedPayroll($employee->company_id);

        $terminationDate = $employee->employeeInfo->termination_date ?? null;
        $formattedTerminationDate = isset($terminationDate) ? Carbon::parse($terminationDate)->format('Y-m-d') : null;
        $beforeCurrentPayroll = $formattedTerminationDate && $this->isBeforeCurrentPayroll($formattedTerminationDate, $currentPayroll);

        $terminationRequest = null;
        $employeeRequest = null;
        $requestedBy = null;
        $actionHasBeenTaken = true;

        if (isset($terminationDate)) {
            $terminationRequest = $this->terminationRequestRepository->getTerminationRequest($employee->id);
            if (isset($terminationRequest)) {
                $actionHasBeenTaken = $this->workflowApprovalCycleRepository->actionHasBeenTaken($terminationRequest->id, 'termination_request');
                $employeeRequest = $this->employeeRequestRepository->getRequestByRequestableId([
                    'requestable_id' => $terminationRequest->id,
                    'requestable_type' => 'termination_request',
                ]);
            }
            $requestedBy = $user->employee->id ?? null;
        }

        // Set the button flags
        $employee->termination_button = $this->shouldShowTerminationButton($isNotTerminated, $hasPermissionToLiteTerminate, $terminationDate);
        $employee->rehire_button = $this->shouldShowRehireButton($isNotTerminated, $hasPermissionToRehire, $beforeCurrentPayroll);
        $employee->cancel_button = $this->shouldShowCancelButton($beforeCurrentPayroll, $hasPermissionToCancel, $terminationDate,
            $isNotTerminated, $hasPermissionToLiteTerminate, $terminationRequest, $actionHasBeenTaken, $requestedBy, $employeeRequest
        );
        $employee->termination_web_button = $this->shouldShowTerminationWebButton($isNotTerminated, $hasPermissionToTerminate, $terminationRequest, $formattedTerminationDate);
        $employee->rehiring_web_button = $this->shouldShowRehiringWebButton($isNotTerminated, $hasPermissionToRehire, $beforeCurrentPayroll);
        $employee->cancel_web_button = $this->shouldShowCancelWebButton($beforeCurrentPayroll, $isNotTerminated, $hasPermissionToCancel, $terminationDate, $terminationRequest);
        $employee->can_reset_device = $this->canResetDeviceId($employee->id) && $hasPermissionToResetDevice;
        $employee->is_egyptian = $employee->employeeInfo->nationality == 'egyptian';
        $titleHistory = $this->getTitleHistory($employee);
        $employee->title_history = $titleHistory;
        $employee->age = $employee->employeeInfo->birth_date ? Carbon::parse($employee->employeeInfo->birth_date)->age : null;

    }

    private function hasPermission($roles, $permission): bool
    {
        return $roles->contains(fn ($role) => $role->hasPermissionTo($permission, 'user-api'));
    }

    private function isBeforeCurrentPayroll(?string $formattedTerminationDate, mixed $currentPayroll): bool
    {
        return isset($formattedTerminationDate, $currentPayroll['start']) && $formattedTerminationDate < $currentPayroll['start'];
    }

    private function shouldShowTerminationButton(bool $isNotTerminated, bool $hasPermissionToLiteTerminate, $terminationDate): bool
    {
        return $isNotTerminated && $hasPermissionToLiteTerminate && is_null($terminationDate);
    }

    private function shouldShowRehireButton(bool $isNotTerminated, bool $hasPermissionToRehire, bool $beforeCurrentPayroll): bool
    {
        return ! $isNotTerminated && $hasPermissionToRehire && $beforeCurrentPayroll;
    }

    private function shouldShowCancelButton(bool $beforeCurrentPayroll, bool $hasPermissionToCancel, $terminationDate,
        bool $isNotTerminated, bool $hasPermissionToLiteTerminate, $terminationRequest,
        bool $actionHasBeenTaken, ?int $requestedBy, $employeeRequest): bool
    {
        // Direct cancel logic
        $canDirectCancel = ! $beforeCurrentPayroll &&
            $hasPermissionToCancel &&
            ! is_null($terminationDate) &&
            $terminationRequest?->status == 'approved';

        // Lite cancel logic
        $canLiteCancel = ! $actionHasBeenTaken &&
            $hasPermissionToLiteTerminate &&
            $isNotTerminated &&
            ! is_null($terminationRequest?->workflow_id) &&
            ($requestedBy && $employeeRequest && $requestedBy === $employeeRequest->requested_by);

        return $canDirectCancel || $canLiteCancel;
    }

    private function shouldShowTerminationWebButton(bool $isNotTerminated, bool $hasPermissionToTerminate, $terminationRequest, $terminationDate): bool
    {
        return $isNotTerminated && $hasPermissionToTerminate && (is_null($terminationDate) || $terminationRequest?->status == 'pending');
    }

    private function shouldShowRehiringWebButton(bool $isNotTerminated, bool $hasPermissionToRehire, bool $beforeCurrentPayroll): bool
    {
        return ! $isNotTerminated && $hasPermissionToRehire && $beforeCurrentPayroll;
    }

    private function shouldShowCancelWebButton(bool $beforeCurrentPayroll, bool $isNotTerminated, bool $hasPermissionToCancel, $terminationDate, $terminationRequest): bool
    {
        return ! $beforeCurrentPayroll && $hasPermissionToCancel && ! is_null($terminationDate) && $terminationRequest?->status == 'approved';
    }

    private function canResetDeviceId($employeeId)
    {
        $startOfMonth = now()->startOfMonth();
        $endOfMonth = now()->endOfMonth();

        $deviceReseted = DeviceIdResetHistory::where('employee_id', $employeeId)
            ->where('made_by', auth()->user()->employee->id)
            ->whereBetween('created_at', [$startOfMonth, $endOfMonth])
            ->exists();

        return ! $deviceReseted;
    }

    public function resetDeviceId($id)
    {
        $employee = $this->repository->find($id);
        if (is_null($employee)) {
            throw new UnprocessableException('Employee not found');
        }
        if (! $this->canResetDeviceId($id)) {
            throw new UnprocessableException('Device ID has already been reset for this employee this month');
        }
        $user = $employee->user;
        $user->device_id = null;
        $user->save();
        DeviceIdResetHistory::create([
            'company_id' => $employee->company_id,
            'made_by' => auth()->user()->employee->id,
            'employee_id' => $id,
        ]);
    }

    public function getScopeEmployees($request)
    {
        $lang = (config('globals.lang') ?? 'ar');
        $search = $request->get('search');
        if ($request->has('employee_id')) {
            $id = $request->employee_id;
        } else {
            $id = auth()->user()->employee_id;
        }
        $employee = $this->repository->find($id);

        if (is_null($employee)) {
            throw new UnprocessableException(trans('messages.employee_not_found'));
        }

        $scope = $this->getUserHighestScopeKey($employee->user);
        $employees = $this->newEmployeeRepository->getEmployeeUnderScope($employee, $scope, $search);

        return [
            'employees' => $employees,
            'scope' => $scope,
            'branches' => $employee->relationLoaded('branches') || $employee->branches()->exists()
                ? $employee->branches->map(function ($branch) use ($lang) {
                    return [
                        'id' => $branch->id,
                        'name' => $lang == 'en' ? $branch->name_en : $branch->name_ar,
                    ];
                })->toArray() : [],
            'departments' => $employee->relationLoaded('managedDepartments') || $employee->managedDepartments()->exists()
                ? $employee->managedDepartments->map(function ($department) use ($lang) {
                    return [
                        'id' => $department->id,
                        'name' => $lang == 'en' ? $department->name_en : $department->name_ar,
                    ];
                })->toArray() : [],
            'sub_departments' => $employee->relationLoaded('managedSubDepartments') || $employee->managedSubDepartments()->exists()
                ? $employee->managedSubDepartments->map(function ($subDepartment) use ($lang) {
                    return [
                        'id' => $subDepartment->id,
                        'name' => $lang == 'en' ? $subDepartment->name_en : $subDepartment->name_ar,
                    ];
                })->toArray() : [],
        ];
    }

    public function getScopeEmployeesForLoans($request = null)
    {
        $employee = auth()->user()->employee;

        $scope = $this->getUserHighestScopeKey($employee->user);
        $policyType = $request ? $request->get('policy_type') : null;
        $employees = $this->newEmployeeRepository->getEmployeeUnderScopeForLoans($employee, $scope, $policyType);

        return ScopeEmployeeForLoansResource::collection($employees);
    }

    public function getTitleHistory($employee)
    {
        $titleHistory = $this->changeTitleRepository->getTitleHistoryByEmployeeId($employee->id);
        $startDate = $employee->employeeInfo->join_date;
        $titleHistoryData = [];
        $currentTitle = $employee->title;
        $lang = (config('globals.lang') ?? 'ar');

        if (count($titleHistory) > 0) {
            $lastIndex = count($titleHistory) - 1;
            foreach ($titleHistory as $history) {
                $pastTitle = $history->fromTitle;
                $byEmployee = $history->byEmployee;
                $titleHistoryData[] = [
                    'title' => [
                        'id' => $pastTitle->id,
                        'name' => $lang == 'en' ? $pastTitle->name_en : $pastTitle->name_ar,
                        'name_localized' => [
                            'en' => $pastTitle->name_en,
                            'ar' => $pastTitle->name_ar,
                        ],
                    ],
                    'start_date' => $startDate,
                    'end_date' => $history->as_of_date,
                    'by' => [
                        'id' => $byEmployee->id,
                        'name' => $lang == 'en' ? $byEmployee->name_en : $byEmployee->name_ar,
                        'name_localized' => [
                            'en' => $byEmployee->name_en,
                            'ar' => $byEmployee->name_ar,
                        ],
                    ],
                    'type' => $history->type,
                ];
                $startDate = $history->as_of_date;
            }
            $byEmployee = $titleHistory[$lastIndex]->byEmployee;

            return [
                'current_title' => [
                    'title' => [
                        'id' => $currentTitle->id ?? null,
                        'name' => $lang == 'en' ? $currentTitle?->name_en : $currentTitle?->name_ar,
                        'name_localized' => [
                            'en' => $currentTitle?->name_en,
                            'ar' => $currentTitle?->name_ar,
                        ],
                    ],
                    'start_date' => $titleHistory[$lastIndex]->as_of_date,
                    'end_date' => 'current',
                    'by' => [
                        'id' => $byEmployee->id,
                        'name' => $lang == 'en' ? $byEmployee->name_en : $byEmployee->name_ar,
                        'name_localized' => [
                            'en' => $byEmployee->name_en,
                            'ar' => $byEmployee->name_ar,
                        ],
                    ],
                    'type' => $titleHistory[$lastIndex]->type,
                ],
                'history' => $titleHistoryData,
            ];
        } else {
            // Handle case when there's no history
            return [
                'current_title' => [
                    'title' => [
                        'id' => $currentTitle->id ?? null,
                        'name' => $lang == 'en' ? $currentTitle?->name_en : $currentTitle?->name_ar,
                    ],
                    'start_date' => $startDate,
                    'end_date' => 'current',
                    'by' => null, // No previous change by anyone
                    'type' => null, // No type since there's no history
                ],
                'history' => [], // No history records
            ];
        }
    }

    public function getEmployeeRequests($id)
    {
        $employee = $this->repository->find($id);
        if (is_null($employee)) {
            throw new UnprocessableException(trans('messages.employee_not_found'));
        }

        $employeeRequestGroup = $employee->title->requestGroup;
        $employeeRequestGroup->load('requestWorkflows.role');

        $allFilteredRoles = collect(); // Collection to store roles with their filtered employees

        $employeeRequestGroup->requestWorkflows->map(function ($workflow) use ($employee, &$allFilteredRoles) {
            $role = $workflow->role;
            $role->load(['employees', 'scopes']);

            $highestScopeKey = '';
            $roleScopes = $role->scopes ?? [];

            foreach ($roleScopes as $roleScope) {
                $highestScopeKey = $this->compareScopes($highestScopeKey, $roleScope->key);
            }

            $filteredEmployees = $role->employees->filter(function ($roleEmployee) use ($employee, $highestScopeKey) {
                if ($highestScopeKey === 'company') {
                    return true;
                } elseif ($highestScopeKey === 'branch') {
                    return $roleEmployee->branches->contains('id', $employee->branch->id ?? null);
                } elseif ($highestScopeKey === 'department') {
                    return $roleEmployee->managedDepartments->contains('id', $employee->title->department->id ?? null);
                } elseif ($highestScopeKey === 'sub_department') {
                    return $roleEmployee->managedSubDepartments->contains('id', $employee->title->subDepartment->id ?? null);
                }

                return false;
            });

            $allFilteredRoles->push([
                'role' => $role,
                'employees' => $filteredEmployees->unique('id'),
            ]);
        });

        $employeeRequestGroup->filtered_roles = $allFilteredRoles; // Attach roles with filtered employees

        return $employeeRequestGroup;
    }

    public function getEmployeeIncentives($employeeId)
    {
        $employeeIncentives = $this->incentivePerEmployeeRepository->getIncentiveSettingsWithParametersByEmployeeId($employeeId);

        return $employeeIncentives;

    }

    public function saveFaceIdImage($filePath, $employeeFaceImageId)
    {
        $fileUrl = $this->getFileUrl($filePath);
        $attachmentData = $this->prepareFaceIdAttachment($filePath, $employeeFaceImageId);

        return $this->attachmentRepository->upsert(
            $attachmentData,
            ['attachable_id' => $employeeFaceImageId,
                'attachable_type' => 'employee_face_id'],
            ['path' => $filePath,
                'attachment_url' => $fileUrl]
        )->get();
    }

    public function prepareFaceIdAttachment($filePath, $employeeFaceImageId)
    {
        $fileUrl = $this->getFileUrl($filePath);

        return $attachmentData = [
            'attachable_type' => 'employee_face_id',
            'attachable_id' => $employeeFaceImageId,
            'path' => $filePath,
            'attachment_url' => $fileUrl,
            'created_at' => date('Y-m-d H:i:s'),
            'updated_at' => date('Y-m-d H:i:s'),
        ];
    }

    public function registerEmployeeFaceId($data)
    {
        return DB::transaction(function () use ($data) {
            $employee = $this->repository->find($data['employee_id']);
            if (is_null($employee)) {
                throw new UnprocessableException('Employee not found');
            }

            $companyUuid = $employee->company->uuid;
            $employeeUuid = $employee->uuid;
            $companyId = $employee->company_id;

            if (! isset($data['face_id_image'])) {
                return null;
            }

            $filePath = $this->uploadFile($data['face_id_image'], 's3', "companies_face_images/{$companyUuid}/{$employeeUuid}/face_id_image");

            EmployeeFaceIdImage::insert([
                'employee_id' => $data['employee_id'],
                'image_path' => $filePath,
                'company_id' => $companyId,
            ]);

            $employeeFaceImageId = EmployeeFaceIdImage::where('employee_id', $data['employee_id'])->first()->id;

            return $this->saveFaceIdImage($filePath, $employeeFaceImageId);
        });
    }

    public function verifyEmployeePin($data)
    {
        $user = auth()->user();
        if ($user->pin != $data['pin']) {
            throw new UnprocessableException('Invalid pin');
        } else {
            return true;
        }

    }

    public function updateProfile(int $id, array $data)
    {
        $employee = $this->repository->find($id);
        if (is_null($employee)) {
            throw new UnprocessableException(trans('messages.employee_not_found'));
        }
        $employeeInfo = $employee->employeeInfo;
        if (! $employeeInfo) {
            $employeeInfo = new EmployeeInfo;
            $employeeInfo->employee_id = $employee->id;
        }

        $result = null;
        if (isset($data['basic_info'])) {
            $this->updateBasicInfo($employee, $data['basic_info']);
        }

        if (isset($data['personal_details'])) {
            $this->updatePersonalDetails($employee, $employeeInfo, $data['personal_details']);
        }

        if (isset($data['contact_details'])) {
            $result = $this->updateContactDetails($employee, $employeeInfo, $data['contact_details']);
        }

        if (isset($data['contract_details'])) {
            $result = $this->updateContractDetails($employee, $employeeInfo, $data['contract_details']);
        }

        if (isset($data['education'])) {
            $result = $this->updateEducation($employee, $data['education']);
        }

        if (isset($data['training_details'])) {
            $this->updateTrainingDetails($employeeInfo, $data['training_details']);
        }

        if (isset($data['management_scope'])) {
            $this->setupManagedEntities($employee, $data['management_scope']);
        }
        $employee->save();

        $employee->refresh();
        if (isset($employee)) {
            $employee->load('employeeInfo');
            dispatch(new FillEmployeeBaseBalancesJob([$employee]));
        }
        $employeeInfo->save();
        $this->updateRelatedEntities($employee);

        return $result;
    }

    private function updateBasicInfo($employee, array $data)
    {
        $employee->first_name_ar = trim($data['first_name_ar']);
        $employee->second_name_ar = trim($data['second_name_ar']);
        $employee->third_name_ar = trim($data['third_name_ar']);
        $employee->fourth_name_ar =
        (isset($data['fourth_name_ar'])
        && ! empty(trim($data['fourth_name_ar'])))
        ? trim($data['fourth_name_ar'])
        : null;

        $employee->fifth_name_ar =
        (isset($data['fifth_name_ar']) && ! empty(trim($data['fifth_name_ar'])))
        ? trim($data['fifth_name_ar'])
        : null;

        $employee->first_name = $employee->first_name_ar;
        $employee->second_name = $employee->second_name_ar;
        $employee->third_name = $employee->third_name_ar;
        $employee->fourth_name = $employee->fourth_name_ar;
        $employee->fifth_name = $employee->fifth_name_ar;

        $first_name_en =
        (isset($data['first_name_en']) && ! empty(trim($data['first_name_en'])))
        ? trim($data['first_name_en'])
        : null;

        $second_name_en =
        (isset($data['second_name_en']) && ! empty(trim($data['second_name_en'])))
        ? trim($data['second_name_en'])
        : null;

        $third_name_en =
        (isset($data['third_name_en']) && ! empty(trim($data['third_name_en'])))
        ? trim($data['third_name_en'])
        : null;

        $fourth_name_en =
        (isset($data['fourth_name_en']) && ! empty(trim($data['fourth_name_en'])))
        ? trim($data['fourth_name_en'])
        : null;

        $fifth_name_en =
        (isset($data['fifth_name_en']) && ! empty(trim($data['fifth_name_en'])))
        ? trim($data['fifth_name_en'])
        : null;

        $employee->first_name_en = $first_name_en;
        $employee->second_name_en = $second_name_en;
        $employee->third_name_en = $third_name_en;
        $employee->fourth_name_en = $fourth_name_en;
        $employee->fifth_name_en = $fifth_name_en;

        $employee->employee_number = $data['employee_number'];
        $employee->is_trackable = $data['is_trackable'];

        $employee->name_ar = $employee->first_name_ar.' '.$employee->second_name_ar.' '.$employee->third_name_ar;
        if (! empty($employee->fourth_name_ar)) {
            $employee->name_ar .= ' '.$employee->fourth_name_ar;
        }
        if (! empty($employee->fifth_name_ar)) {
            $employee->name_ar .= ' '.$employee->fifth_name_ar;
        }
        if (! empty($first_name_en) && ! empty($second_name_en) && ! empty($third_name_en)) {
            $employee->name_en = $first_name_en.' '.$second_name_en.' '.$third_name_en;
            if (! empty($fourth_name_en)) {
                $employee->name_en .= ' '.$fourth_name_en;
            }
            if (! empty($fifth_name_en)) {
                $employee->name_en .= ' '.$fifth_name_en;
            }
        } else {
            $employee->name_en = null;
        }

        $employee->name = $employee->name_ar;

        if ($employee->user) { // Remove condition if user always exist
            $employee->user->name = $employee->name_ar;
            $employee->user->save();
        }
        if (isset($data['rehire_date']) && ! empty($data['rehire_date'])) {
            $this->terminationService->employeeRehire($employee->id, $data);
            $this->terminationService->fixLeaveBalancesForRehiredEmployee($employee->id);
        }
    }

    private function updatePersonalDetails($employee, $employeeInfo, array $data)
    {
        $employeeInfo->nationality = $data['nationality'] ?? null;
        $employeeInfo->birth_date = $data['birth_date'] ?? null;
        $employeeInfo->gender = $data['gender'];
        $employeeInfo->address = $data['address'] ?? null;
        $employeeInfo->place_of_birth = $data['place_of_birth'] ?? null;
        $employeeInfo->religion = $data['religion'] ?? null;
        if (isset($data['religion'])) {
            $employeeInfo->other_religion = ($data['religion'] === ReligionEnum::OTHER->value) ? ($data['other_religion'] ?? null) : null;
        } else {
            $employeeInfo->other_religion = null;
        }
        if (isset($data['nationality']) && $data['nationality'] === Nationalities::EGYPTIAN->value) {
            $employeeInfo->military_status = $data['military_status'] ?? null;
        } else {
            $employeeInfo->military_status = null;
        }
        $employeeInfo->marital_status = $data['marital_status'] ?? null;
        $employeeInfo->passport_number = $data['passport_number'] ?? null;
        if (isset($data['marital_status'])) {
            if ($data['marital_status'] !== MaritalStatusEnum::SINGLE->value) {
                $employeeInfo->number_kids = $data['number_kids'];
            } elseif ($data['marital_status'] === MaritalStatusEnum::SINGLE->value) {
                $employeeInfo->number_kids = 0;
            }
        } else {
            $employeeInfo->number_kids = 0;
        }
        $employee->national_id = $data['national_id'] ?? null;
        $employeeInfo->notes = $data['notes'] ?? null;
    }

    private function updateContactDetails($employee, $employeeInfo, array $data)
    {
        $phoneCountryCode = $data['phone_country_code'];
        $phone = $data['phone'];
        $employee->phone = $this->formatPhoneWithCountryCode($phoneCountryCode, $phone);

        if (isset($data['secondary_phone'])) {
            $secondaryPhoneCountryCode = $data['secondary_phone_country_code'];
            $secondaryPhone = $data['secondary_phone'];
            $employeeInfo->secondary_phone = $this->formatPhoneWithCountryCode($secondaryPhoneCountryCode, $secondaryPhone);
        } else {
            $employeeInfo->secondary_phone = null;
        }
        $employeeInfo->email = $data['work_email'] ?? null;
        $employeeInfo->personal_email = $data['personal_email'] ?? null;

        if (isset($data['emergency_contacts'])) {
            if (isset($data['emergency_contacts']['items'])) {
                $contactsToCreate = [];

                foreach ($data['emergency_contacts']['items'] as $item) {
                    if (empty($item['phone']) && empty($item['relation']) && empty($item['name'])) {
                        continue;
                    }
                    $contactPhoneCountryCode = $item['phone_country_code'];
                    $contactPhone = $item['phone'];

                    $contactData = [
                        'name' => $item['name'] ?? null,
                        'phone' => $this->formatPhoneWithCountryCode($contactPhoneCountryCode, $contactPhone),
                        'relation' => $item['relation'] ?? null,
                    ];

                    if (isset($item['id'])) {
                        $this->emergencyContactService->update($item['id'], $contactData);
                    } else {
                        $contactData['employee_id'] = $employee->id;
                        $contactData['company_id'] = config('globals.company')->id;
                        $contactsToCreate[] = $contactData;
                    }
                }

                if (! empty($contactsToCreate)) {
                    $this->emergencyContactService->insertMany($contactsToCreate);
                }
            }

            if (isset($data['emergency_contacts']['delete_ids'])) {
                $this->emergencyContactService->delete($data['emergency_contacts']['delete_ids']);
            }
        }

        return EmployeeEmergencyContactResource::collection($employee->emergencyContacts);
    }

    private function updateContractDetails($employee, $employeeInfo, array $data)
    {
        $employeeInfo->join_date = $data['join_date'];
        $payroll = config('globals.company')?->latestPayroll->first() ?? null;
        $employee->status = EmployeesService::getEmployeeStatus($data['join_date'] ?? null, $payroll);

        $employeeInfo->employment_type = $data['employment_type'] ?? null;

        if (isset($data['contracts'])) {
            if (isset($data['contracts']['items'])) {
                $contractsToCreate = [];

                foreach ($data['contracts']['items'] as $item) {
                    $contractData = [
                        'contract_start_date' => $item['contract_start_date'],
                        'contract_duration' => $item['contract_duration'],
                    ];

                    $contractData['contract_end_date'] = $this->calculateContractEndDate(
                        $item['contract_duration'],
                        $item['contract_start_date'],
                        $item['contract_end_date'] ?? null
                    );

                    if (isset($item['id'])) {
                        $this->contractService->update($item['id'], $contractData);
                    } else {
                        $contractData['employee_id'] = $employee->id;
                        $contractData['company_id'] = config('globals.company')->id;
                        $contractsToCreate[] = $contractData;
                    }
                }

                if (! empty($contractsToCreate)) {
                    $this->contractService->insertMany($contractsToCreate);
                }
            }

            if (isset($data['contracts']['delete_ids'])) {
                $this->contractService->delete($data['contracts']['delete_ids']);
            }
        }

        return EmployeeContractResource::collection($employee->contracts);
    }

    private function updateEducation($employee, array $data)
    {
        if (isset($data['items'])) {
            $educationsToCreate = [];

            foreach ($data['items'] as $item) {
                if (empty($item['degree_type']) && empty($item['degree_name']) && empty($item['institution_name']) && empty($item['graduation_year'])) {
                    continue;
                }
                $educationData = [
                    'degree_type' => $item['degree_type'],
                    'degree_name' => $item['degree_name'] ?? null,
                    'institution_name' => $item['institution_name'] ?? null,
                    'graduation_year' => $item['graduation_year'] ?? null,
                ];

                if (isset($item['id'])) {
                    $this->educationService->update($item['id'], $educationData);
                } else {
                    $educationData['employee_id'] = $employee->id;
                    $educationData['company_id'] = config('globals.company')->id;
                    $educationsToCreate[] = $educationData;
                }
            }

            if (! empty($educationsToCreate)) {
                $this->educationService->insertMany($educationsToCreate);
            }
        }

        if (isset($data['delete_ids'])) {
            $this->educationService->delete($data['delete_ids']);
        }

        return EmployeeEducationResource::collection($employee->education);
    }

    private function updateTrainingDetails($employeeInfo, array $data)
    {
        $employeeInfo->training_certification_status = $data['training_certification_status'] ?? null;
    }

    public function updateRelatedEntities($employee)
    {
        $yearsOfExperience = $employee->employeeInfo->number_of_years_of_experience ?? null;
        $age = Carbon::parse($employee?->employeeInfo?->birth_date)->age ?? null;

        $companyAnnualLeaveId = auth()->user()->company->annual_leave_id;

        $employeeHasAddedHoursForAnnualLeave = $employee->employeeLeaveBalances()
            ->where('company_leave_type_id', $companyAnnualLeaveId)
            ->whereDate('start', '<=', now())
            ->whereDate('end', '>=', now())
            ->where('added_hours', '>', 0)
            ->exists();

        if ($yearsOfExperience > 10 || $age > 50) {

            if (! $employeeHasAddedHoursForAnnualLeave) {
                $employeeLeaveBalance = $employee->employeeLeaveBalances()
                    ->where('company_leave_type_id', $companyAnnualLeaveId)
                    ->whereDate('start', '<=', now())
                    ->whereDate('end', '>=', now())
                    ->where('added_hours', '=', 0)
                    ->first();

                if ($employeeLeaveBalance) {
                    $newBalance = $employeeLeaveBalance->balance + (9 * 8); // Assuming 8 hours per day for 9 days
                    $employeeLeaveBalance->update([
                        'balance' => $newBalance,
                        'added_hours' => 9 * 8, // Update added_hours as well
                    ]);
                    $employeeLeaveBalance->save();

                }

            }

        } else {

            if ($employeeHasAddedHoursForAnnualLeave) {
                $employeeLeaveBalance = $employee->employeeLeaveBalances()
                    ->where('company_leave_type_id', $companyAnnualLeaveId)
                    ->whereDate('start', '<=', now())
                    ->whereDate('end', '>=', now())
                    ->where('added_hours', '>', 0)
                    ->first();

                if ($employeeLeaveBalance) {
                    $newBalance = $employeeLeaveBalance->balance - (9 * 8); // Assuming 8 hours per day for 9 days
                    $employeeLeaveBalance->update([
                        'balance' => $newBalance,
                        'added_hours' => 0, // Update added_hours as well
                    ]);
                }

            }

        }
    }

    public function addProfile(array $data)
    {
        $employee = $this->repository->add($this->prepareEmployeeData($data));
        $this->createEmployeeInfo($employee, $data);

        $this->setupUserAndRoles($employee, $data);

        $this->setupManagedEntities($employee, $data);

        if (isset($data['emergency_contacts'])) {
            $this->createEmergencyContacts($employee, $data['emergency_contacts']);
        }

        if (isset($data['education'])) {
            $this->createEducationRecords($employee, $data['education']);
        }

        if ($data['include_contract_details']) {
            $this->createContractRecords($employee, $data);
        }
        if (! empty($employee->title->net_salary)) {
            $this->employeeSalaryRepository->add([
                'employee_id' => $employee->id,
                'basic_salary' => $employee->title->net_salary,
                'gross_salary' => $employee->title->gross_salary,
                'net_salary' => $employee->title->net_salary,
                'as_of_date' => Carbon::now()->format('Y-m-d'),
                'by_employee_id' => config('globals.user')->employee_id,
                'salary_disbursement_method' => 'cash',
            ]);
        }

        if (isset($employee)) {
            $employee->load('employeeInfo');
            dispatch(new FillEmployeeBaseBalancesJob([$employee]));
        }

        $dummyOutput = new \stdClass;
        $this->employeeLeaveRequestCrudService->addProratedRequest(['employee_id' => $employee->id], $dummyOutput);

        $this->employeesService->generateTimecardsIfStaticTitle($employee->id, $employee->title);

        $this->incentiveService->maintainIncentivePerEmployeeTable($employee->id, $data['branch_id'], $employee->title_id, config('globals.company')->id);

        return $employee;
    }

    private function prepareEmployeeData(array $data): array
    {
        $employeeNumber = $data['employee_number'] ?? $this->employeesService->getRecentEmployeeCode();

        $first_name_en =
        (isset($data['first_name_en']) && ! empty(trim($data['first_name_en'])))
        ? trim($data['first_name_en'])
        : null;

        $second_name_en =
        (isset($data['second_name_en']) && ! empty(trim($data['second_name_en'])))
        ? trim($data['second_name_en'])
        : null;

        $third_name_en =
        (isset($data['third_name_en']) && ! empty(trim($data['third_name_en'])))
        ? trim($data['third_name_en'])
        : null;

        $fourth_name_en =
        (isset($data['fourth_name_en']) && ! empty(trim($data['fourth_name_en'])))
        ? trim($data['fourth_name_en'])
        : null;

        $fifth_name_en =
        (isset($data['fifth_name_en']) && ! empty(trim($data['fifth_name_en'])))
        ? trim($data['fifth_name_en'])
        : null;

        $employeeData = [
            'first_name_ar' => trim($data['first_name_ar']),
            'second_name_ar' => trim($data['second_name_ar']),
            'third_name_ar' => trim($data['third_name_ar']),
            'fourth_name_ar' => (isset($data['fourth_name_ar']) && ! empty(trim($data['fourth_name_ar'])))
            ? trim($data['fourth_name_ar'])
            : null,

            'fifth_name_ar' => (isset($data['fifth_name_ar']) && ! empty(trim($data['fifth_name_ar'])))
            ? trim($data['fifth_name_ar'])
            : null,

            'first_name_en' => $first_name_en,

            'second_name_en' => $second_name_en,

            'third_name_en' => $third_name_en,

            'fourth_name_en' => $fourth_name_en,

            'fifth_name_en' => $fifth_name_en,

            'first_name' => trim($data['first_name_ar']),
            'second_name' => trim($data['second_name_ar']),
            'third_name' => trim($data['third_name_ar']),
            'fourth_name' => (isset($data['fourth_name_ar']) && ! empty(trim($data['fourth_name_ar']))) ? trim($data['fourth_name_ar']) : null,
            'fifth_name' => (isset($data['fifth_name_ar']) && ! empty(trim($data['fifth_name_ar']))) ? trim($data['fifth_name_ar']) : null,
            'employee_number' => $employeeNumber,
            'title_id' => $data['title_id'],
            'branch_id' => $data['branch_id'],
            'is_trackable' => $data['is_trackable'],
            'national_id' => $data['national_id'] ?? null,
            'company_id' => config('globals.company')->id,
        ];

        $employeeData['name_ar'] = $employeeData['first_name_ar'].' '.$employeeData['second_name_ar'].' '.$employeeData['third_name_ar'];
        if (! empty($employeeData['fourth_name_ar'])) {
            $employeeData['name_ar'] .= ' '.$employeeData['fourth_name_ar'];
        }
        if (! empty($employeeData['fifth_name_ar'])) {
            $employeeData['name_ar'] .= ' '.$employeeData['fifth_name_ar'];
        }

        if (! empty($first_name_en) && ! empty($second_name_en) && ! empty($third_name_en)) {
            $employeeData['name_en'] = $first_name_en.' '.$second_name_en.' '.$third_name_en;
            if (! empty($fourth_name_en)) {
                $employeeData['name_en'] .= ' '.$fourth_name_en;
            }
            if (! empty($fifth_name_en)) {
                $employeeData['name_en'] .= ' '.$fifth_name_en;
            }
        } else {
            $employeeData['name_en'] = null;
        }

        $employeeData['name'] = $employeeData['name_ar'];

        $phoneCountryCode = $data['phone_country_code'];
        $phone = $data['phone'];
        $employeeData['phone'] = $this->formatPhoneWithCountryCode($phoneCountryCode, $phone);

        $payroll = config('globals.company')?->latestPayroll->first() ?? null;
        $employeeData['status'] = EmployeesService::getEmployeeStatus($data['join_date'] ?? null, $payroll);

        $title = $this->titlesRepository->findOrFail($data['title_id']);
        $uuid = Uuid::uuid7();
        $uuidString = str_replace('-', '', $uuid->toString());
        $employeeData['uuid'] = substr($uuidString, 0, 16);
        $endProbationDate = Carbon::parse($data['join_date'])
            ->addMonths($title->probation_period)
            ->startOfDay();

        $employeeData['on_probation'] = ! ($title->probation_period > 0 && isset($endProbationDate) && $endProbationDate < date('Y-m-d'));

        return $employeeData;
    }

    private function createEmployeeInfo($employee, array $data)
    {
        $employeeInfo = new EmployeeInfo;
        $employeeInfo->employee_id = $employee->id;

        $employeeInfo->nationality = $data['nationality'] ?? null;
        $employeeInfo->birth_date = $data['birth_date'] ?? null;
        $employeeInfo->gender = $data['gender'] ?? null;
        $employeeInfo->address = $data['address'] ?? null;
        $employeeInfo->place_of_birth = $data['place_of_birth'] ?? null;
        $employeeInfo->religion = $data['religion'] ?? null;
        if (isset($data['religion'])) {
            $employeeInfo->other_religion = ($data['religion'] === ReligionEnum::OTHER->value) ? ($data['other_religion'] ?? null) : null;
        } else {
            $employeeInfo->other_religion = null;
        }
        if (isset($data['nationality']) && $data['nationality'] === Nationalities::EGYPTIAN->value) {
            $employeeInfo->military_status = $data['military_status'] ?? null;
        } else {
            $employeeInfo->military_status = null;
        }
        $employeeInfo->marital_status = $data['marital_status'] ?? null;
        $employeeInfo->passport_number = $data['passport_number'] ?? null;

        if (isset($data['marital_status']) && $data['marital_status'] !== MaritalStatusEnum::SINGLE->value) {
            $employeeInfo->number_kids = $data['number_kids'] ?? null;
        } else {
            $employeeInfo->number_kids = 0;
        }

        $employeeInfo->email = $data['work_email'] ?? null;
        $employeeInfo->personal_email = $data['personal_email'] ?? null;

        if (isset($data['secondary_phone'])) {
            $secondaryPhoneCountryCode = $data['secondary_phone_country_code'];
            $secondaryPhone = $data['secondary_phone'];
            $employeeInfo->secondary_phone = $this->formatPhoneWithCountryCode($secondaryPhoneCountryCode, $secondaryPhone);
        }

        $employeeInfo->join_date = $data['join_date'];
        $employeeInfo->employment_type = $data['employment_type'] ?? null;
        $employeeInfo->number_of_years_of_experience = $data['years_of_experience'] ?? null;
        $employeeInfo->notes = $data['notes'] ?? null;
        $employeeInfo->training_certification_status = $data['training_certification_status'] ?? null;
        $this->employeeInfoRepository->add($employeeInfo->toArray());

        return $employeeInfo;
    }

    private function setupUserAndRoles($employee, array $data): void
    {
        $roleId = $data['role_id'] ?? $employee->title->role_id ?? null;

        $userRequest = [
            'employee_id' => $employee->id,
            'role_id' => $roleId,
            'name' => $employee->name_ar,
        ];
        $dummyOutput = new \stdClass;
        $this->userCrudService->create($userRequest, $dummyOutput);

        $registerationValidationRequest = ['name' => $employee->name, 'phone' => $employee->phone, 'user_id' => $dummyOutput->user->id];
        $dummyOutput = new \stdClass;
        $this->registerationValidationCrudService->create($registerationValidationRequest, $dummyOutput);

        if (isset($roleId)) {
            $employee->user->assignRole([$roleId]);
        }
    }

    private function setupManagedEntities($employee, array $data): void
    {
        $highestScopeKey = $this->getUserHighestScopeKey($employee->user);

        if ($highestScopeKey == ScopeUtil::COMPANY_SCOPE) {
            $this->syncAllBranchesToCompanyScopeEmployee($employee);
        } elseif ($highestScopeKey != ScopeUtil::ME_SCOPE) {
            if ($highestScopeKey == ScopeUtil::DEPARTMENT_SCOPE) {
                $employee->managedDepartments()->sync($data['managed_department_ids']);
            } elseif ($highestScopeKey == ScopeUtil::SUB_DEPARTMENT_SCOPE) {
                $employee->managedSubDepartments()->sync($data['managed_sub_department_ids']);
            }
            $employee->branches()->sync($data['managed_branch_ids']);
        }
    }

    private function createEmergencyContacts($employee, array $data): void
    {
        $contactsToCreate = [];

        foreach ($data as $item) {
            if (empty($item['phone']) && empty($item['relation']) && empty($item['name'])) {
                continue;
            }

            $contactPhoneCountryCode = $item['phone_country_code'];
            $contactPhone = $item['phone'];

            $contactsToCreate[] = [
                'employee_id' => $employee->id,
                'company_id' => config('globals.company')->id,
                'name' => $item['name'] ?? null,
                'phone' => $this->formatPhoneWithCountryCode($contactPhoneCountryCode, $contactPhone),
                'relation' => $item['relation'] ?? null,
            ];
        }

        if (! empty($contactsToCreate)) {
            $this->emergencyContactService->insertMany($contactsToCreate);
        }
    }

    private function createEducationRecords($employee, array $data): void
    {
        $educationsToCreate = [];

        foreach ($data as $item) {
            if (empty($item['degree_type']) && empty($item['degree_name']) && empty($item['institution_name']) && empty($item['graduation_year'])) {
                continue;
            }

            $educationsToCreate[] = [
                'employee_id' => $employee->id,
                'company_id' => config('globals.company')->id,
                'degree_type' => $item['degree_type'],
                'degree_name' => $item['degree_name'] ?? null,
                'institution_name' => $item['institution_name'] ?? null,
                'graduation_year' => $item['graduation_year'] ?? null,
            ];
        }

        if (! empty($educationsToCreate)) {
            $this->educationService->insertMany($educationsToCreate);
        }
    }

    private function createContractRecords($employee, array $data): void
    {
        $contractData = [
            'employee_id' => $employee->id,
            'company_id' => config('globals.company')->id,
        ];
        if ($data['contract_start_date_same_as_join_date']) {
            $contractData['contract_start_date'] = $data['join_date'];
        } else {
            $contractData['contract_start_date'] = $data['contract_start_date'];
        }
        $contractData['contract_duration'] = $data['contract_duration'];

        $contractData['contract_end_date'] = $this->calculateContractEndDate(
            $contractData['contract_duration'],
            $contractData['contract_start_date'],
            $data['contract_end_date'] ?? null
        );

        $this->contractService->add($contractData);
    }

    private function syncAllBranchesToCompanyScopeEmployee($employee): void
    {
        $branches = $this->branchRepository->all()->pluck('id')->toArray();
        $employee->branches()->sync($branches);
    }

    private function calculateContractEndDate(string $contractDuration, string $contractStartDate, ?string $customEndDate = null): ?string
    {
        if ($contractDuration === ContractDurationEnum::CUSTOM->value) {
            return $customEndDate;
        } elseif ($contractDuration === ContractDurationEnum::TWELVE_MONTHS->value) {
            return date('Y-m-d', strtotime($contractStartDate.' + 12 months'));
        } elseif ($contractDuration === ContractDurationEnum::SIX_MONTHS->value) {
            return date('Y-m-d', strtotime($contractStartDate.' + 6 months'));
        } elseif ($contractDuration === ContractDurationEnum::THREE_MONTHS->value) {
            return date('Y-m-d', strtotime($contractStartDate.' + 3 months'));
        } else {
            return null;
        }
    }
}
