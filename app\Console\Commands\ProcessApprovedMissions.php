<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use App\Models\MissionRequest;
use App\Repositories\V1\Attendance\AttendanceRepository;
use App\Services\V1\HandleActionOnMissionService;
use Carbon\Carbon;
use App\Enums\Missions\MissionsEnum;
use App\Models\Employee;
use Illuminate\Support\Facades\DB;

class ProcessApprovedMissions extends Command
{
    protected $signature = 'missions:process-approved {--dry-run : Do not execute mission approval flow}';

    protected $description = 'Process approved mission requests without timecards and run the approval flow.';

    public function __construct(
        private AttendanceRepository $attendanceRepository,
    ) {
        parent::__construct();
    }

    public function handle()
    {
        $dryRun = $this->option('dry-run');

        $missions = MissionRequest::where('status', config('globals.REQUEST_STATUSES.APPROVED'))
            ->whereDate('from', '>=', '2025-06-01')
            ->whereNull('timecard_id')
            ->with(['employeeRequest', 'employeeRequest.deciderAdmin', 'workflowApprovalCycles'])
            ->get();

        $halfDayCount = 0;
        $fullDayCount = 0;

        foreach ($missions as $mission) {
            config(['globals.user' => null]);
            $missionDate = Carbon::parse($mission->from)->toDateString();
            // check if employee already has attendance on mission date
            $employeeAttended = $this->attendanceRepository->employeeAttendedOnDay($mission->employee_id, $missionDate);
            $this->info("Employee attended on {$missionDate}: " . ($employeeAttended ? 'yes' : 'no'));
            // skip full-day missions when attendance exists
            if ($employeeAttended && $mission->mission_type == MissionsEnum::FULL_DAY->value) {
                $this->info("Skipping full-day mission {$mission->id}: attendance exists on {$missionDate}");
                continue;
            }

            if ($mission->mission_type == MissionsEnum::FULL_DAY->value) {
                $fullDayCount++;
            } else {
                $halfDayCount++;
            }

            $this->info("Processing mission {$mission->id}");
            $this->info("Employee Request ID: {$mission->employeeRequest->id}");
            // determine decider employee (fallback to latest approved cycle)
            $deciderEmployee = $mission->employeeRequest->deciderAdmin;
            if (! $deciderEmployee) {
                $latestCycle = collect($mission->workflowApprovalCycles)
                    ->where('status', config('globals.REQUEST_STATUSES.APPROVED'))
                    ->sortByDesc('order')
                    ->first();
                if (isset($latestCycle->decider_id)) {
                    $deciderEmployee = Employee::find($latestCycle->decider_id);
                }
            }
            if ($deciderEmployee && $deciderEmployee->user) {
                $deciderUser = $deciderEmployee->user;
                $this->info("Impersonating user: {$deciderUser->id}");
            } else {
                $this->warn("No decider user found for mission {$mission->id}");
            }
            // log each approval cycle
            foreach ($mission->workflowApprovalCycles as $cycle) {
                $this->info("Cycle ID: {$cycle->id} | Role ID: {$cycle->role_id} | Status: {$cycle->status} | Order: {$cycle->order}");
            }
            if (! $dryRun && isset($deciderUser)) {
                DB::transaction(function () use ($mission, $deciderUser) {
                    // reset statuses to pending so the mission can re-enter the approval flow
                    $this->info("Resetting statuses to pending for mission {$mission->id} and its workflows");
                    // impersonate and run approval flow
                    config(['globals.user' => $deciderUser]);
                    app(HandleActionOnMissionService::class)->handleActionOnMission([
                        'mission_request' => $mission,
                        'final_status' => config('globals.REQUEST_STATUSES.APPROVED'),
                        'attendance_data' => null,
                    ]);
                    // clear impersonation
                    config(['globals.user' => null]);
                });
                $this->info("Mission {$mission->id} resolved for approval flow.");
            } elseif (! $dryRun) {
                $this->warn("Skipping mission {$mission->id}: no decider user to impersonate");
            } else {
                $this->info("Dry run: mission {$mission->id} not executed.");
            }
        }

        $this->info("Full day missions processed: {$fullDayCount}");
        $this->info("Half day missions processed: {$halfDayCount}");

        return 0;
    }
} 