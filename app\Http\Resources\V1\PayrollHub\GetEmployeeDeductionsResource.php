<?php

namespace App\Http\Resources\V1\PayrollHub;

use App\FeatureToggles\Unleash;
use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class GetEmployeeDeductionsResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @return array<string, mixed>
     */
    public function toArray(Request $request): array
    {
        $this->resource = (object) $this->resource;
        $unleash = app(Unleash::class);
        if($unleash->isEarlyClockoutDeductionEnabled()){
            return [
                "late_incidents" => $this->late,
                "early_clock_out_incidents" => $this->early_clock_out,
                "missed_clockout_incidents" => 0,
                "late_deductions" => AttendanceDeductionResource::collection($this->late_deductions) ?? [],
                "total_late_deductions_amount" => $this->late_deductions->total_amount,
                "early_clock_out_deductions" => AttendanceDeductionResource::collection($this->early_clock_out_deductions ?? []),
                "total_early_clock_out_deductions_amount" => $this->early_clock_out_deductions->total_amount,
                "employee" => new EmployeeResource($this->employee),
            ];
        }
        return [
            "late_incidents" => $this->late,
            // "early_clock_out_incidents" => $this->early_clock_out,
            "missed_clockout_incidents" => 0,
            "late_deductions" => AttendanceDeductionResource::collection($this->all_total_deductions) ?? [],
            "total_amount" => $this->all_total_deductions->total_amount,
            "employee" => new EmployeeResource($this->employee),
        ];
    }
}
