<?php

namespace App\Enums\V2\WorkTypes;

enum WorkTypesEnum: string
{
    case SHIFT_BASED = 'shift_based';

    case FIXED_WORKING_HOURS = 'fixed_working_hours';

    case FLEXIBLE_WORKING_HOURS = 'flexible_working_hours';

    // make function to return all values name it all()
    public static function all(): array
    {
        return [
            self::SHIFT_BASED->value,
            self::FIXED_WORKING_HOURS->value,
            self::FLEXIBLE_WORKING_HOURS->value,
        ];
    }

    public static function fixedWorkTypes(): array
    {
        return [
            self::FIXED_WORKING_HOURS->value
        ];
    }}
